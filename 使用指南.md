# 🎯 Android 密码填充和 Passkey 应用使用指南

## 📱 应用功能概述

这个应用有两个主要作用：
1. **密码管理器** - 存储你的用户名和密码
2. **系统服务** - 为其他应用提供自动填充功能

## 🚀 快速开始（5分钟体验）

### 第一步：打开应用
1. 安装并打开应用
2. 点击 **"📚 使用教程（推荐先看）"** 按钮

### 第二步：启用自动填充服务（最重要！）
1. 在教程页面点击 **"启用服务"**
2. 系统会打开设置页面
3. 选择 **"Passkey Test"** 作为自动填充服务
4. 确保开关已开启
5. 返回应用，状态应显示 ✅

### 第三步：添加测试密码
1. 点击 **"添加密码"** 按钮
2. 填写测试数据：
   - 网站：`test.com`
   - 用户名：`<EMAIL>`
   - 密码：`123456`
3. 点击 **"保存密码"**
4. 返回教程页面，状态应显示 ✅

### 第四步：测试自动填充
1. 在教程页面点击 **"测试自动填充"**
2. 进入测试登录页面
3. **关键步骤**：点击用户名输入框
4. 应该会弹出自动填充选项
5. 选择你刚才保存的账户
6. 用户名和密码会自动填入！

## 🔍 详细功能说明

### 1. 自动填充功能的工作原理

**什么是自动填充？**
- 当你在其他应用中遇到登录表单时
- 点击用户名或密码输入框
- 系统会显示保存的账户信息
- 选择后自动填入用户名和密码

**在哪里可以使用？**
- 浏览器（Chrome、Firefox等）
- 社交应用（微信、QQ等）
- 购物应用（淘宝、京东等）
- 任何有登录功能的应用

### 2. 密码管理器功能

**添加密码：**
- 网站：填写网站域名或应用名称
- 用户名：你的账户名或邮箱
- 密码：对应的密码

**管理密码：**
- 查看：点击密码字段显示真实密码
- 删除：点击删除按钮移除单个密码
- 清空：删除所有保存的密码

### 3. Passkey 功能

**什么是Passkey？**
- 新一代身份验证技术
- 使用生物识别（指纹/面部）替代密码
- 更安全、更便捷

**如何使用：**
1. 确保设备支持生物识别
2. 输入用户名，点击"创建Passkey"
3. 完成生物识别验证
4. 使用"Passkey认证"进行登录测试

### 4. 网页交互功能

**本地测试：**
- 内置WebAuthn测试页面
- 可以测试网页端的Passkey功能

**外部网站：**
- 可以访问支持Passkey的网站
- 测试真实的网页交互

## 🛠️ 故障排除

### 问题1：看不到自动填充选项
**可能原因：**
- 自动填充服务未启用
- 没有保存相关的密码数据
- 应用字段识别失败

**解决方法：**
1. 检查系统设置中的自动填充服务
2. 确保已保存对应网站的密码
3. 尝试重启应用

### 问题2：Passkey功能不工作
**可能原因：**
- 设备不支持生物识别
- 未设置指纹或面部识别
- 权限不足

**解决方法：**
1. 进入系统设置设置生物识别
2. 确保应用有生物识别权限
3. 重新尝试创建Passkey

### 问题3：网页功能无法使用
**可能原因：**
- 网络连接问题
- WebView权限不足

**解决方法：**
1. 检查网络连接
2. 确保应用有网络权限
3. 尝试访问其他网站

## 📋 测试清单

完成以下测试确保功能正常：

### 基础功能
- [ ] 应用正常启动
- [ ] 教程页面显示正常
- [ ] 所有按钮可以点击

### 自动填充测试
- [ ] 系统设置中已启用服务
- [ ] 成功保存测试密码
- [ ] 在测试页面能看到自动填充选项
- [ ] 可以选择并填入密码
- [ ] 在其他应用中也能使用

### Passkey测试
- [ ] 生物识别设置正常
- [ ] 成功创建Passkey
- [ ] 认证功能正常工作

### 网页测试
- [ ] WebView正常加载
- [ ] 本地测试页面功能正常
- [ ] 可以访问外部网站

## 💡 使用技巧

1. **批量导入密码**：可以逐个添加常用网站的密码
2. **安全建议**：这是演示应用，不要保存重要密码
3. **测试建议**：先用测试数据验证功能
4. **学习目的**：观察自动填充的工作机制

## 🎓 学习价值

通过这个应用你可以学习到：
- Android AutofillService的实现原理
- 生物识别API的使用方法
- WebAuthn协议的基本概念
- 密码管理器的设计思路

## 📞 需要帮助？

如果遇到问题：
1. 首先查看故障排除部分
2. 检查设备兼容性（Android 8.0+）
3. 确保必要的权限已授予
4. 尝试重新安装应用

记住：这是一个学习和演示工具，主要目的是帮助理解Android密码管理和Passkey技术！
