@echo off
echo "快速修复测试..."
echo "=================="

echo "1. 清理项目..."
call gradlew.bat clean

echo "2. 编译项目..."
call gradlew.bat assembleDebug

if %ERRORLEVEL% EQU 0 (
    echo "=================="
    echo "✅ 编译成功！所有错误已修复"
    echo ""
    echo "修复的问题："
    echo "• 添加了缺失的Intent导入"
    echo "• 修复了Manifest合并错误"
    echo "• 移除了重复的Activity声明"
    echo ""
    echo "APK位置: app\build\outputs\apk\debug\app-debug.apk"
    echo ""
    echo "🚀 现在可以安装和测试了："
    echo "adb install app\build\outputs\apk\debug\app-debug.apk"
) else (
    echo "=================="
    echo "❌ 编译失败，请检查错误信息"
)

pause
