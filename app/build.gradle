plugins {
    alias(libs.plugins.android.application)
}

android {
    namespace 'com.isprint.passkey_test'
    compileSdk 35

    defaultConfig {
        applicationId "com.isprint.passkey_test"
        minSdk 26
        targetSdk 35
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildFeatures {
        viewBinding true
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
}

dependencies {

    implementation libs.appcompat
    implementation libs.material

    // Fragment support for BiometricPrompt
    implementation 'androidx.fragment:fragment:1.6.2'

    // CardView for UI
    implementation 'androidx.cardview:cardview:1.0.0'

    // RecyclerView for lists
    implementation 'androidx.recyclerview:recyclerview:1.3.2'

    // Biometric authentication
    implementation 'androidx.biometric:biometric:1.1.0'

    // WebView support
    implementation 'androidx.webkit:webkit:1.8.0'

    // JSON parsing
    implementation 'com.google.code.gson:gson:2.10.1'

    testImplementation libs.junit
    androidTestImplementation libs.ext.junit
    androidTestImplementation libs.espresso.core
}