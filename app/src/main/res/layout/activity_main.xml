<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="24dp"
    android:gravity="center">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Passkey 测试应用"
        android:textSize="24sp"
        android:textStyle="bold"
        android:layout_marginBottom="32dp"
        android:textColor="@android:color/black" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="这个应用演示了Android的密码填充和Passkey功能"
        android:textSize="16sp"
        android:textAlignment="center"
        android:layout_marginBottom="16dp"
        android:textColor="@android:color/darker_gray" />

    <Button
        android:id="@+id/btn_tutorial"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="📚 使用教程（推荐先看）"
        android:textSize="16sp"
        android:layout_marginBottom="24dp"
        android:padding="16dp"
        android:background="@android:color/holo_orange_light"
        android:textColor="@android:color/white" />

    <Button
        android:id="@+id/btn_password_manager"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="密码管理器"
        android:textSize="16sp"
        android:layout_marginBottom="16dp"
        android:padding="16dp" />

    <Button
        android:id="@+id/btn_passkey_demo"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="🔐 Passkey 演示"
        android:textSize="16sp"
        android:layout_marginBottom="16dp"
        android:padding="16dp"
        android:background="@android:color/holo_green_light"
        android:textColor="@android:color/white" />

    <Button
        android:id="@+id/btn_web_demo"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="网页交互演示"
        android:textSize="16sp"
        android:layout_marginBottom="16dp"
        android:padding="16dp" />

    <Button
        android:id="@+id/btn_settings"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="设置"
        android:textSize="16sp"
        android:layout_marginBottom="16dp"
        android:padding="16dp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="提示：首次使用前请先进入设置页面配置相关权限"
        android:textSize="12sp"
        android:textAlignment="center"
        android:layout_marginTop="24dp"
        android:textColor="@android:color/holo_orange_dark" />

</LinearLayout>
