<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="24dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="设置"
            android:textSize="24sp"
            android:textStyle="bold"
            android:layout_marginBottom="24dp"
            android:textColor="@android:color/black" />

        <Button
            android:id="@+id/btn_autofill_settings"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="自动填充设置"
            android:textSize="16sp"
            android:layout_marginBottom="16dp"
            android:padding="16dp" />

        <Button
            android:id="@+id/btn_credential_settings"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="凭据管理器设置"
            android:textSize="16sp"
            android:layout_marginBottom="16dp"
            android:padding="16dp" />

        <Button
            android:id="@+id/btn_biometric_settings"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="生物识别设置"
            android:textSize="16sp"
            android:layout_marginBottom="24dp"
            android:padding="16dp" />

        <TextView
            android:id="@+id/tv_instructions"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="14sp"
            android:lineSpacingExtra="4dp"
            android:textColor="@android:color/darker_gray" />

    </LinearLayout>

</ScrollView>
