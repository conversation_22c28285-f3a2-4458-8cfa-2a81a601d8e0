<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="📚 使用教程"
            android:textSize="24sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="24dp"
            android:textColor="@android:color/black" />

        <!-- 步骤1 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:elevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="步骤1: 启用自动填充服务"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp"
                    android:textColor="@android:color/black" />

                <TextView
                    android:id="@+id/tv_step1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="❌ 自动填充服务未启用"
                    android:textSize="14sp"
                    android:layout_marginBottom="12dp"
                    android:textColor="@android:color/darker_gray" />

                <Button
                    android:id="@+id/btn_step1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="启用服务" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 步骤2 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:elevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="步骤2: 添加测试密码"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp"
                    android:textColor="@android:color/black" />

                <TextView
                    android:id="@+id/tv_step2"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="❌ 未保存任何密码"
                    android:textSize="14sp"
                    android:layout_marginBottom="12dp"
                    android:textColor="@android:color/darker_gray" />

                <Button
                    android:id="@+id/btn_step2"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="添加密码" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 步骤3 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:elevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="步骤3: 测试Passkey功能"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp"
                    android:textColor="@android:color/black" />

                <TextView
                    android:id="@+id/tv_step3"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="❌ 生物识别不可用"
                    android:textSize="14sp"
                    android:layout_marginBottom="12dp"
                    android:textColor="@android:color/darker_gray" />

                <Button
                    android:id="@+id/btn_step3"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="测试Passkey" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 步骤4 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            android:elevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="步骤4: 测试网页交互"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp"
                    android:textColor="@android:color/black" />

                <TextView
                    android:id="@+id/tv_step4"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="测试WebAuthn功能"
                    android:textSize="14sp"
                    android:layout_marginBottom="12dp"
                    android:textColor="@android:color/darker_gray" />

                <Button
                    android:id="@+id/btn_step4"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="打开网页测试" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 测试按钮 -->
        <Button
            android:id="@+id/btn_test_autofill"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="测试自动填充"
            android:textSize="16sp"
            android:padding="16dp"
            android:background="@android:color/holo_green_light"
            android:textColor="@android:color/white"
            android:enabled="false" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="💡 提示：完成前面的步骤后，点击上面的按钮测试自动填充功能"
            android:textSize="12sp"
            android:layout_marginTop="16dp"
            android:gravity="center"
            android:textColor="@android:color/darker_gray" />

    </LinearLayout>

</ScrollView>
