<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="🌐 Chrome浏览器测试"
            android:textSize="24sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="24dp"
            android:textColor="@android:color/black" />

        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:elevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="步骤1: 准备Passkey"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="12dp"
                    android:textColor="@android:color/black" />

                <Button
                    android:id="@+id/btn_create_local_passkey"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="创建测试Passkey"
                    android:textSize="16sp"
                    android:padding="12dp"
                    android:background="@android:color/holo_green_light"
                    android:textColor="@android:color/white" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:elevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="步骤2: 在Chrome中测试"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="12dp"
                    android:textColor="@android:color/black" />

                <Button
                    android:id="@+id/btn_open_chrome_test"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="打开WebAuthn测试网站"
                    android:textSize="16sp"
                    android:padding="12dp"
                    android:layout_marginBottom="8dp"
                    android:background="@android:color/holo_blue_light"
                    android:textColor="@android:color/white" />

                <Button
                    android:id="@+id/btn_open_demo_site"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="打开同事的Demo网站"
                    android:textSize="16sp"
                    android:padding="12dp"
                    android:background="@android:color/holo_purple"
                    android:textColor="@android:color/white" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:elevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="📖 使用说明"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="12dp"
                    android:textColor="@android:color/black" />

                <TextView
                    android:id="@+id/tv_instructions"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textSize="14sp"
                    android:lineSpacingExtra="4dp"
                    android:textColor="@android:color/darker_gray" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

    </LinearLayout>

</ScrollView>
