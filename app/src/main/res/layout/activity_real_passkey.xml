<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="24dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="🔐 Passkey 演示"
            android:textSize="24sp"
            android:textStyle="bold"
            android:layout_marginBottom="16dp"
            android:textColor="@android:color/black" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="这个页面使用真正的Credential Manager API，可以与Google密码管理器交互，创建和使用真实的Passkey。"
            android:textSize="14sp"
            android:layout_marginBottom="24dp"
            android:textColor="@android:color/darker_gray" />

        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            android:elevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="配置信息"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="16dp"
                    android:textColor="@android:color/black" />

                <EditText
                    android:id="@+id/et_rp_id"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="域名 (Relying Party ID)"
                    android:inputType="textUri"
                    android:padding="12dp"
                    android:layout_marginBottom="12dp"
                    android:background="@drawable/result_background" />

                <EditText
                    android:id="@+id/et_username"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="用户名"
                    android:inputType="textEmailAddress"
                    android:padding="12dp"
                    android:layout_marginBottom="16dp"
                    android:background="@drawable/result_background" />

                <Button
                    android:id="@+id/btn_create_passkey"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="创建 Passkey"
                    android:textSize="16sp"
                    android:layout_marginBottom="12dp"
                    android:padding="16dp"
                    android:background="@android:color/holo_green_light"
                    android:textColor="@android:color/white" />

                <Button
                    android:id="@+id/btn_authenticate_passkey"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="使用 Passkey 认证"
                    android:textSize="16sp"
                    android:padding="16dp"
                    android:background="@android:color/holo_blue_light"
                    android:textColor="@android:color/white" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="操作结果："
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginBottom="8dp"
            android:textColor="@android:color/black" />

        <TextView
            android:id="@+id/tv_result"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="等待操作...\n\n💡 提示：\n• 这将使用真正的Credential Manager API\n• 可能会弹出Google密码管理器界面\n• 需要生物识别验证\n• 创建的Passkey会保存到Google账户"
            android:textSize="14sp"
            android:padding="16dp"
            android:background="@drawable/result_background"
            android:textColor="@android:color/black" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="⚠️ 重要说明：\n\n1. 这是真实的Passkey实现，会与Google密码管理器交互\n2. 创建的Passkey会保存到你的Google账户\n3. 需要设备支持生物识别认证\n4. 域名需要配置Digital Asset Links才能在网站上使用"
            android:textSize="12sp"
            android:layout_marginTop="24dp"
            android:padding="12dp"
            android:background="@android:color/holo_orange_light"
            android:textColor="@android:color/white" />

    </LinearLayout>

</ScrollView>
