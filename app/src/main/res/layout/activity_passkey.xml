<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="24dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Passkey 演示"
            android:textSize="24sp"
            android:textStyle="bold"
            android:layout_marginBottom="24dp"
            android:textColor="@android:color/black" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Passkey是一种新的身份验证方式，使用生物识别技术替代传统密码。"
            android:textSize="14sp"
            android:layout_marginBottom="24dp"
            android:textColor="@android:color/darker_gray" />

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="用户名">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_username"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textEmailAddress" />

        </com.google.android.material.textfield.TextInputLayout>

        <Button
            android:id="@+id/btn_create_passkey"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="创建 Passkey"
            android:textSize="16sp"
            android:layout_marginBottom="16dp"
            android:padding="16dp"
            style="@style/Widget.Material3.Button" />

        <Button
            android:id="@+id/btn_authenticate_passkey"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="使用 Passkey 认证"
            android:textSize="16sp"
            android:layout_marginBottom="24dp"
            android:padding="16dp"
            style="@style/Widget.Material3.Button.OutlinedButton" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="操作结果："
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginBottom="8dp"
            android:textColor="@android:color/black" />

        <TextView
            android:id="@+id/tv_result"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="等待操作..."
            android:textSize="14sp"
            android:padding="16dp"
            android:background="@drawable/result_background"
            android:textColor="@android:color/black" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="注意事项：\n• 需要Android 14或更高版本\n• 需要设置生物识别认证\n• 首次创建Passkey时会要求生物识别验证"
            android:textSize="12sp"
            android:layout_marginTop="24dp"
            android:textColor="@android:color/darker_gray" />

    </LinearLayout>

</ScrollView>
