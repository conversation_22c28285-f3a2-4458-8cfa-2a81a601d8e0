<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="24dp"
    android:gravity="center">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="🧪 自动填充测试页面"
        android:textSize="24sp"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginBottom="32dp"
        android:textColor="@android:color/black" />

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:elevation="8dp"
        android:layout_marginBottom="24dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="模拟登录表单"
                android:textSize="18sp"
                android:textStyle="bold"
                android:gravity="center"
                android:layout_marginBottom="24dp"
                android:textColor="@android:color/black" />

            <EditText
                android:id="@+id/et_test_username"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="用户名或邮箱"
                android:inputType="textEmailAddress"
                android:padding="16dp"
                android:layout_marginBottom="16dp"
                android:background="@drawable/result_background" />

            <EditText
                android:id="@+id/et_test_password"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="密码"
                android:inputType="textPassword"
                android:padding="16dp"
                android:layout_marginBottom="24dp"
                android:background="@drawable/result_background" />

            <Button
                android:id="@+id/btn_test_login"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="登录"
                android:textSize="16sp"
                android:padding="16dp" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <TextView
        android:id="@+id/tv_instructions"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="14sp"
        android:lineSpacingExtra="4dp"
        android:textColor="@android:color/darker_gray" />

</LinearLayout>
