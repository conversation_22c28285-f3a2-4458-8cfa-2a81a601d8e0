<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- 网络权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <!-- 生物识别权限 -->
    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
    <uses-permission android:name="android.permission.USE_FINGERPRINT" />

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.Passkey_test"
        tools:targetApi="31">

        <!-- 主Activity -->
        <activity
            android:name=".MainActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- 其他Activity -->
        <activity
            android:name=".TutorialActivity"
            android:exported="false" />

        <activity
            android:name=".TestLoginActivity"
            android:exported="false" />

        <activity
            android:name=".PasswordManagerActivity"
            android:exported="false" />

        <activity
            android:name=".PasskeyActivity"
            android:exported="false" />

        <activity
            android:name=".RealPasskeyActivity"
            android:exported="false" />

        <activity
            android:name=".WebViewActivity"
            android:exported="false" />

        <activity
            android:name=".SettingsActivity"
            android:exported="false" />

        <!-- 自动填充服务 -->
        <service
            android:name=".MyAutofillService"
            android:exported="true"
            android:permission="android.permission.BIND_AUTOFILL_SERVICE">
            <intent-filter>
                <action android:name="android.service.autofill.AutofillService" />
            </intent-filter>
            <meta-data
                android:name="android.autofill"
                android:resource="@xml/autofill_service" />
        </service>

    </application>

</manifest>