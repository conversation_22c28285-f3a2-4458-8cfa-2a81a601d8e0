package com.isprint.passkey_test;

import android.app.PendingIntent;
import android.content.Intent;
import android.os.CancellationSignal;
import android.os.OutcomeReceiver;
import android.service.credentials.BeginCreateCredentialRequest;
import android.service.credentials.BeginCreateCredentialResponse;
import android.service.credentials.BeginGetCredentialRequest;
import android.service.credentials.BeginGetCredentialResponse;
import android.service.credentials.CreateCredentialRequest;
import android.service.credentials.CreateCredentialResponse;
import android.service.credentials.CredentialProviderService;
import android.service.credentials.GetCredentialRequest;
import android.service.credentials.GetCredentialResponse;
import android.util.Log;

import androidx.annotation.NonNull;

public class MyCredentialProviderService extends CredentialProviderService {

    private static final String TAG = "MyCredentialProviderService";

    @Override
    public void onBeginCreateCredential(
            @NonNull BeginCreateCredentialRequest request,
            @NonNull CancellationSignal cancellationSignal,
            @NonNull OutcomeReceiver<BeginCreateCredentialResponse, Exception> callback) {
        
        Log.d(TAG, "onBeginCreateCredential called");
        
        try {
            // 创建一个PendingIntent，指向我们的PasskeyActivity
            Intent intent = new Intent(this, PasskeyActivity.class);
            intent.putExtra("action", "create");
            
            PendingIntent pendingIntent = PendingIntent.getActivity(
                this, 0, intent, 
                PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_MUTABLE
            );

            // 创建响应 - 使用简化的构造方法
            BeginCreateCredentialResponse response = new BeginCreateCredentialResponse();
            callback.onResult(response);
            
        } catch (Exception e) {
            Log.e(TAG, "Error in onBeginCreateCredential", e);
            callback.onError(e);
        }
    }

    @Override
    public void onBeginGetCredential(
            @NonNull BeginGetCredentialRequest request,
            @NonNull CancellationSignal cancellationSignal,
            @NonNull OutcomeReceiver<BeginGetCredentialResponse, Exception> callback) {
        
        Log.d(TAG, "onBeginGetCredential called");
        
        try {
            // 创建响应 - 使用简化的构造方法
            BeginGetCredentialResponse response = new BeginGetCredentialResponse();
            callback.onResult(response);
            
        } catch (Exception e) {
            Log.e(TAG, "Error in onBeginGetCredential", e);
            callback.onError(e);
        }
    }

    @Override
    public void onCreateCredential(
            @NonNull CreateCredentialRequest request,
            @NonNull CancellationSignal cancellationSignal,
            @NonNull OutcomeReceiver<CreateCredentialResponse, Exception> callback) {
        
        Log.d(TAG, "onCreateCredential called");
        callback.onError(new Exception("Use PendingIntent instead"));
    }

    @Override
    public void onGetCredential(
            @NonNull GetCredentialRequest request,
            @NonNull CancellationSignal cancellationSignal,
            @NonNull OutcomeReceiver<GetCredentialResponse, Exception> callback) {
        
        Log.d(TAG, "onGetCredential called");
        callback.onError(new Exception("Use PendingIntent instead"));
    }
}
