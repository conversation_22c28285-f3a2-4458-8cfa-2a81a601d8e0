package com.isprint.passkey_test;

import android.app.PendingIntent;
import android.content.Intent;
import android.graphics.drawable.Icon;
import android.os.Bundle;
import android.os.CancellationSignal;
import android.os.OutcomeReceiver;
import android.service.credentials.BeginCreateCredentialRequest;
import android.service.credentials.BeginCreateCredentialResponse;
import android.service.credentials.BeginGetCredentialRequest;
import android.service.credentials.BeginGetCredentialResponse;
import android.service.credentials.CreateCredentialRequest;
import android.service.credentials.CreateCredentialResponse;
import android.service.credentials.CredentialProviderService;
import android.service.credentials.GetCredentialRequest;
import android.service.credentials.GetCredentialResponse;
import android.util.Log;

import androidx.annotation.NonNull;

public class PasskeyProviderService extends CredentialProviderService {

    private static final String TAG = "PasskeyProviderService";

    @Override
    public void onBeginCreateCredential(
            @NonNull BeginCreateCredentialRequest request,
            @NonNull CancellationSignal cancellationSignal,
            @NonNull OutcomeReceiver<BeginCreateCredentialResponse, Exception> callback) {
        
        Log.d(TAG, "onBeginCreateCredential called");
        
        try {
            // 创建一个PendingIntent，指向我们的PasskeyActivity
            Intent intent = new Intent(this, PasskeyActivity.class);
            intent.putExtra("action", "create");
            intent.putExtra("request_data", request.getData());
            
            PendingIntent pendingIntent = PendingIntent.getActivity(
                this, 0, intent, 
                PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_MUTABLE
            );

            // 创建响应，告诉系统我们可以处理这个请求
            BeginCreateCredentialResponse response = new BeginCreateCredentialResponse.Builder()
                .setCreateEntries(java.util.Collections.singletonList(
                    new android.service.credentials.CreateEntry.Builder(
                        "com.isprint.passkey_test",
                        pendingIntent
                    )
                    .setDescription("使用 Passkey Test 创建 Passkey")
                    .setIcon(Icon.createWithResource(this, R.drawable.ic_key))
                    .build()
                ))
                .build();

            callback.onResult(response);
            
        } catch (Exception e) {
            Log.e(TAG, "Error in onBeginCreateCredential", e);
            callback.onError(e);
        }
    }

    @Override
    public void onBeginGetCredential(
            @NonNull BeginGetCredentialRequest request,
            @NonNull CancellationSignal cancellationSignal,
            @NonNull OutcomeReceiver<BeginGetCredentialResponse, GetCredentialException> callback) {
        
        Log.d(TAG, "onBeginGetCredential called");
        
        try {
            // 创建一个PendingIntent，指向我们的PasskeyActivity
            Intent intent = new Intent(this, PasskeyActivity.class);
            intent.putExtra("action", "get");
            intent.putExtra("request_data", request.getData());
            
            PendingIntent pendingIntent = PendingIntent.getActivity(
                this, 1, intent, 
                PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_MUTABLE
            );

            // 创建响应，告诉系统我们有可用的凭据
            BeginGetCredentialResponse response = new BeginGetCredentialResponse.Builder()
                .setCredentialEntries(java.util.Collections.singletonList(
                    new android.service.credentials.CredentialEntry.Builder(
                        "com.isprint.passkey_test",
                        "Passkey Test",
                        pendingIntent
                    )
                    .setDescription("使用 Passkey Test 进行认证")
                    .setIcon(Icon.createWithResource(this, R.drawable.ic_key))
                    .setLastUsedTime(java.time.Instant.now())
                    .build()
                ))
                .build();

            callback.onResult(response);
            
        } catch (Exception e) {
            Log.e(TAG, "Error in onBeginGetCredential", e);
            callback.onError(new GetCredentialException("Failed to get credentials"));
        }
    }

    @Override
    public void onCreateCredential(
            @NonNull CreateCredentialRequest request,
            @NonNull CancellationSignal cancellationSignal,
            @NonNull OutcomeReceiver<CreateCredentialResponse, Exception> callback) {
        
        Log.d(TAG, "onCreateCredential called");
        // 这个方法通常不会被直接调用，因为我们使用PendingIntent
        callback.onError(new Exception("Use PendingIntent instead"));
    }

    @Override
    public void onGetCredential(
            @NonNull GetCredentialRequest request,
            @NonNull CancellationSignal cancellationSignal,
            @NonNull OutcomeReceiver<GetCredentialResponse, Exception> callback) {
        
        Log.d(TAG, "onGetCredential called");
        // 这个方法通常不会被直接调用，因为我们使用PendingIntent
        callback.onError(new Exception("Use PendingIntent instead"));
    }

    // 内部异常类
    public static class GetCredentialException extends Exception {
        public GetCredentialException(String message) {
            super(message);
        }
    }
}
