package com.isprint.passkey_test;

import android.content.Context;
import android.content.SharedPreferences;
import android.os.Handler;
import android.os.Looper;
import android.util.Base64;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.biometric.BiometricManager;
import androidx.biometric.BiometricPrompt;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentActivity;

import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.concurrent.Executor;

public class PasskeyManager {

    private static final String TAG = "PasskeyManager";
    private static final String PREFS_NAME = "passkey_prefs";
    private final Context context;
    private final SharedPreferences prefs;
    private final Handler mainHandler;

    public PasskeyManager(Context context) {
        this.context = context;
        this.prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        this.mainHandler = new Handler(Looper.getMainLooper());
    }

    public interface PasskeyCallback {
        void onSuccess(String result);
        void onError(String error);
    }

    public void createPasskey(String username, PasskeyCallback callback) {
        Log.d(TAG, "Creating passkey for user: " + username);

        // 检查生物识别是否可用
        BiometricManager biometricManager = BiometricManager.from(context);
        switch (biometricManager.canAuthenticate(BiometricManager.Authenticators.BIOMETRIC_WEAK)) {
            case BiometricManager.BIOMETRIC_SUCCESS:
                Log.d(TAG, "App can authenticate using biometrics.");
                break;
            case BiometricManager.BIOMETRIC_ERROR_NO_HARDWARE:
                callback.onError("设备不支持生物识别");
                return;
            case BiometricManager.BIOMETRIC_ERROR_HW_UNAVAILABLE:
                callback.onError("生物识别硬件当前不可用");
                return;
            case BiometricManager.BIOMETRIC_ERROR_NONE_ENROLLED:
                callback.onError("用户未设置生物识别");
                return;
        }

        if (context instanceof FragmentActivity) {
            FragmentActivity activity = (FragmentActivity) context;

            Executor executor = ContextCompat.getMainExecutor(context);
            BiometricPrompt biometricPrompt = new BiometricPrompt(activity, executor,
                    new BiometricPrompt.AuthenticationCallback() {
                        @Override
                        public void onAuthenticationError(int errorCode, @NonNull CharSequence errString) {
                            super.onAuthenticationError(errorCode, errString);
                            callback.onError("生物识别错误: " + errString);
                        }

                        @Override
                        public void onAuthenticationSucceeded(@NonNull BiometricPrompt.AuthenticationResult result) {
                            super.onAuthenticationSucceeded(result);

                            // 模拟创建Passkey - 保存到本地存储
                            String passkeyId = generatePasskeyId();
                            SharedPreferences.Editor editor = prefs.edit();
                            editor.putString("passkey_" + username, passkeyId);
                            editor.putLong("passkey_" + username + "_created", System.currentTimeMillis());
                            editor.apply();

                            Log.d(TAG, "Passkey created and saved locally for user: " + username);
                            callback.onSuccess("Passkey创建成功！已为用户 " + username + " 创建Passkey");
                        }

                        @Override
                        public void onAuthenticationFailed() {
                            super.onAuthenticationFailed();
                            callback.onError("生物识别验证失败");
                        }
                    });

            BiometricPrompt.PromptInfo promptInfo = new BiometricPrompt.PromptInfo.Builder()
                    .setTitle("创建Passkey")
                    .setSubtitle("请验证您的身份以创建Passkey")
                    .setNegativeButtonText("取消")
                    .build();

            biometricPrompt.authenticate(promptInfo);
        } else {
            callback.onError("需要在Activity中使用此功能");
        }
    }

    public void authenticateWithPasskey(String username, PasskeyCallback callback) {
        Log.d(TAG, "Authenticating with passkey for user: " + username);

        // 检查是否存在该用户的Passkey
        String passkeyId = prefs.getString("passkey_" + username, null);
        if (passkeyId == null) {
            callback.onError("未找到用户 " + username + " 的Passkey，请先创建");
            return;
        }

        // 检查生物识别是否可用
        BiometricManager biometricManager = BiometricManager.from(context);
        switch (biometricManager.canAuthenticate(BiometricManager.Authenticators.BIOMETRIC_WEAK)) {
            case BiometricManager.BIOMETRIC_SUCCESS:
                Log.d(TAG, "App can authenticate using biometrics.");
                break;
            case BiometricManager.BIOMETRIC_ERROR_NO_HARDWARE:
                callback.onError("设备不支持生物识别");
                return;
            case BiometricManager.BIOMETRIC_ERROR_HW_UNAVAILABLE:
                callback.onError("生物识别硬件当前不可用");
                return;
            case BiometricManager.BIOMETRIC_ERROR_NONE_ENROLLED:
                callback.onError("用户未设置生物识别");
                return;
        }

        if (context instanceof FragmentActivity) {
            FragmentActivity activity = (FragmentActivity) context;

            Executor executor = ContextCompat.getMainExecutor(context);
            BiometricPrompt biometricPrompt = new BiometricPrompt(activity, executor,
                    new BiometricPrompt.AuthenticationCallback() {
                        @Override
                        public void onAuthenticationError(int errorCode, @NonNull CharSequence errString) {
                            super.onAuthenticationError(errorCode, errString);
                            callback.onError("生物识别错误: " + errString);
                        }

                        @Override
                        public void onAuthenticationSucceeded(@NonNull BiometricPrompt.AuthenticationResult result) {
                            super.onAuthenticationSucceeded(result);

                            // 模拟Passkey认证成功
                            long createdTime = prefs.getLong("passkey_" + username + "_created", 0);
                            Log.d(TAG, "Passkey authentication successful for user: " + username);
                            callback.onSuccess("Passkey认证成功！用户 " + username + " 已通过生物识别验证\n" +
                                    "Passkey ID: " + passkeyId + "\n" +
                                    "创建时间: " + new java.util.Date(createdTime));
                        }

                        @Override
                        public void onAuthenticationFailed() {
                            super.onAuthenticationFailed();
                            callback.onError("生物识别验证失败");
                        }
                    });

            BiometricPrompt.PromptInfo promptInfo = new BiometricPrompt.PromptInfo.Builder()
                    .setTitle("Passkey认证")
                    .setSubtitle("请验证您的身份以使用Passkey登录")
                    .setNegativeButtonText("取消")
                    .build();

            biometricPrompt.authenticate(promptInfo);
        } else {
            callback.onError("需要在Activity中使用此功能");
        }
    }

    private String generatePasskeyId() {
        byte[] randomBytes = new byte[32];
        new SecureRandom().nextBytes(randomBytes);
        return Base64.encodeToString(randomBytes, Base64.URL_SAFE | Base64.NO_WRAP | Base64.NO_PADDING);
    }

    private byte[] generateChallenge() {
        byte[] challenge = new byte[32];
        new SecureRandom().nextBytes(challenge);
        return challenge;
    }
}
