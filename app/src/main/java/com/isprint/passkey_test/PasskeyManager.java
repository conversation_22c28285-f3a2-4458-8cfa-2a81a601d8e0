package com.isprint.passkey_test;

import android.content.Context;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.credentials.CreateCredentialRequest;
import androidx.credentials.CreateCredentialResponse;
import androidx.credentials.CreatePublicKeyCredentialRequest;
import androidx.credentials.CredentialManager;
import androidx.credentials.CredentialManagerCallback;
import androidx.credentials.GetCredentialRequest;
import androidx.credentials.GetCredentialResponse;
import androidx.credentials.GetPublicKeyCredentialOption;
import androidx.credentials.PublicKeyCredential;
import androidx.credentials.exceptions.CreateCredentialException;
import androidx.credentials.exceptions.GetCredentialException;

import com.google.gson.Gson;
import com.google.gson.JsonObject;

import android.util.Base64;

import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

public class PasskeyManager {

    private static final String TAG = "PasskeyManager";
    private final Context context;
    private final CredentialManager credentialManager;
    private final Executor executor;
    private final Gson gson;

    public PasskeyManager(Context context) {
        this.context = context;
        this.credentialManager = CredentialManager.create(context);
        this.executor = Executors.newSingleThreadExecutor();
        this.gson = new Gson();
    }

    public interface PasskeyCallback {
        void onSuccess(String result);
        void onError(String error);
    }

    public void createPasskey(String username, PasskeyCallback callback) {
        Log.d(TAG, "Creating passkey for user: " + username);

        // 生成挑战
        byte[] challenge = generateChallenge();
        String challengeBase64 = Base64.encodeToString(challenge, Base64.URL_SAFE | Base64.NO_WRAP | Base64.NO_PADDING);

        // 生成用户ID
        byte[] userId = username.getBytes(StandardCharsets.UTF_8);
        String userIdBase64 = Base64.encodeToString(userId, Base64.URL_SAFE | Base64.NO_WRAP | Base64.NO_PADDING);

        // 构建创建凭据的JSON
        JsonObject createCredentialJson = new JsonObject();
        createCredentialJson.addProperty("challenge", challengeBase64);
        createCredentialJson.addProperty("timeout", 60000);

        // RP信息
        JsonObject rp = new JsonObject();
        rp.addProperty("id", "demo.i-sprint.com");
        rp.addProperty("name", "I-Sprint Passkey Demo");
        createCredentialJson.add("rp", rp);

        // 用户信息
        JsonObject user = new JsonObject();
        user.addProperty("id", userIdBase64);
        user.addProperty("name", username);
        user.addProperty("displayName", username);
        createCredentialJson.add("user", user);

        // 公钥凭据参数
        JsonObject pubKeyCredParams = new JsonObject();
        pubKeyCredParams.addProperty("type", "public-key");
        pubKeyCredParams.addProperty("alg", -7); // ES256
        createCredentialJson.add("pubKeyCredParams", gson.toJsonTree(new JsonObject[]{pubKeyCredParams}));

        // 认证器选择
        JsonObject authenticatorSelection = new JsonObject();
        authenticatorSelection.addProperty("authenticatorAttachment", "platform");
        authenticatorSelection.addProperty("requireResidentKey", true);
        authenticatorSelection.addProperty("residentKey", "required");
        authenticatorSelection.addProperty("userVerification", "required");
        createCredentialJson.add("authenticatorSelection", authenticatorSelection);

        // 证明
        createCredentialJson.addProperty("attestation", "none");

        String requestJson = gson.toJson(createCredentialJson);
        Log.d(TAG, "Create credential request JSON: " + requestJson);

        CreatePublicKeyCredentialRequest createRequest = new CreatePublicKeyCredentialRequest(requestJson);

        credentialManager.createCredentialAsync(
                context,
                createRequest,
                null,
                executor,
                new CredentialManagerCallback<CreateCredentialResponse, CreateCredentialException>() {
                    @Override
                    public void onResult(CreateCredentialResponse result) {
                        Log.d(TAG, "Passkey created successfully");
                        if (result instanceof PublicKeyCredential) {
                            PublicKeyCredential credential = (PublicKeyCredential) result;
                            callback.onSuccess("Passkey创建成功: " + credential.getAuthenticationResponseJson());
                        } else {
                            callback.onSuccess("Passkey创建成功");
                        }
                    }

                    @Override
                    public void onError(@NonNull CreateCredentialException e) {
                        Log.e(TAG, "Failed to create passkey", e);
                        callback.onError("创建Passkey失败: " + e.getMessage());
                    }
                }
        );
    }

    public void authenticateWithPasskey(String username, PasskeyCallback callback) {
        Log.d(TAG, "Authenticating with passkey for user: " + username);

        // 生成挑战
        byte[] challenge = generateChallenge();
        String challengeBase64 = Base64.encodeToString(challenge, Base64.URL_SAFE | Base64.NO_WRAP | Base64.NO_PADDING);

        // 构建获取凭据的JSON
        JsonObject getCredentialJson = new JsonObject();
        getCredentialJson.addProperty("challenge", challengeBase64);
        getCredentialJson.addProperty("timeout", 60000);
        getCredentialJson.addProperty("rpId", "demo.i-sprint.com");
        getCredentialJson.addProperty("userVerification", "required");

        String requestJson = gson.toJson(getCredentialJson);
        Log.d(TAG, "Get credential request JSON: " + requestJson);

        GetPublicKeyCredentialOption getOption = new GetPublicKeyCredentialOption(requestJson);
        GetCredentialRequest getRequest = new GetCredentialRequest.Builder()
                .addCredentialOption(getOption)
                .build();

        credentialManager.getCredentialAsync(
                context,
                getRequest,
                null,
                executor,
                new CredentialManagerCallback<GetCredentialResponse, GetCredentialException>() {
                    @Override
                    public void onResult(GetCredentialResponse result) {
                        Log.d(TAG, "Passkey authentication successful");
                        if (result.getCredential() instanceof PublicKeyCredential) {
                            PublicKeyCredential credential = (PublicKeyCredential) result.getCredential();
                            callback.onSuccess("Passkey认证成功: " + credential.getAuthenticationResponseJson());
                        } else {
                            callback.onSuccess("Passkey认证成功");
                        }
                    }

                    @Override
                    public void onError(@NonNull GetCredentialException e) {
                        Log.e(TAG, "Failed to authenticate with passkey", e);
                        callback.onError("Passkey认证失败: " + e.getMessage());
                    }
                }
        );
    }

    private byte[] generateChallenge() {
        byte[] challenge = new byte[32];
        new SecureRandom().nextBytes(challenge);
        return challenge;
    }
}
