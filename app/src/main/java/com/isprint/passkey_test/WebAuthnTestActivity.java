package com.isprint.passkey_test;

import android.os.Bundle;
import android.webkit.JavascriptInterface;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

public class WebAuthnTestActivity extends AppCompatActivity {

    private WebView webView;
    private PasskeyManager passkeyManager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_webauthn_test);

        passkeyManager = new PasskeyManager(this);
        initWebView();
        loadTestPage();
    }

    private void initWebView() {
        webView = findViewById(R.id.webview);
        WebSettings webSettings = webView.getSettings();
        webSettings.setJavaScriptEnabled(true);
        webSettings.setDomStorageEnabled(true);
        
        // 添加JavaScript接口，让网页可以调用我们的app
        webView.addJavascriptInterface(new WebAppInterface(), "AndroidPasskey");
        
        webView.setWebViewClient(new WebViewClient() {
            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                // 注入JavaScript代码，重写WebAuthn API
                injectPasskeyScript();
            }
        });
    }

    private void loadTestPage() {
        String html = "<!DOCTYPE html>\n" +
                "<html>\n" +
                "<head>\n" +
                "    <title>Passkey Test</title>\n" +
                "    <meta name='viewport' content='width=device-width, initial-scale=1'>\n" +
                "    <style>\n" +
                "        body { font-family: Arial, sans-serif; padding: 20px; }\n" +
                "        .container { max-width: 400px; margin: 0 auto; }\n" +
                "        button { width: 100%; padding: 15px; margin: 10px 0; font-size: 16px; }\n" +
                "        .success { background: #4CAF50; color: white; }\n" +
                "        .error { background: #f44336; color: white; }\n" +
                "        input { width: 100%; padding: 10px; margin: 5px 0; }\n" +
                "        #result { margin: 20px 0; padding: 10px; border-radius: 5px; }\n" +
                "    </style>\n" +
                "</head>\n" +
                "<body>\n" +
                "    <div class='container'>\n" +
                "        <h1>🔐 Passkey 测试页面</h1>\n" +
                "        <p>这个页面会直接调用我们的Android应用！</p>\n" +
                "        \n" +
                "        <input type='text' id='username' placeholder='用户名' value='<EMAIL>'>\n" +
                "        \n" +
                "        <button onclick='createPasskey()'>创建 Passkey</button>\n" +
                "        <button onclick='authenticatePasskey()'>使用 Passkey 认证</button>\n" +
                "        \n" +
                "        <div id='result'></div>\n" +
                "    </div>\n" +
                "    \n" +
                "    <script>\n" +
                "        function showResult(message, isSuccess) {\n" +
                "            const result = document.getElementById('result');\n" +
                "            result.textContent = message;\n" +
                "            result.className = isSuccess ? 'success' : 'error';\n" +
                "        }\n" +
                "        \n" +
                "        function createPasskey() {\n" +
                "            const username = document.getElementById('username').value;\n" +
                "            if (!username) {\n" +
                "                showResult('请输入用户名', false);\n" +
                "                return;\n" +
                "            }\n" +
                "            \n" +
                "            showResult('正在创建Passkey...', true);\n" +
                "            \n" +
                "            // 调用Android应用\n" +
                "            AndroidPasskey.createPasskey(username, 'webauthn-test.local');\n" +
                "        }\n" +
                "        \n" +
                "        function authenticatePasskey() {\n" +
                "            showResult('正在进行Passkey认证...', true);\n" +
                "            \n" +
                "            // 调用Android应用\n" +
                "            AndroidPasskey.authenticatePasskey('webauthn-test.local');\n" +
                "        }\n" +
                "        \n" +
                "        // 从Android应用接收结果\n" +
                "        function onPasskeyResult(success, message) {\n" +
                "            showResult(message, success);\n" +
                "        }\n" +
                "    </script>\n" +
                "</body>\n" +
                "</html>";

        webView.loadData(html, "text/html", "UTF-8");
    }

    private void injectPasskeyScript() {
        // 这里可以注入更复杂的WebAuthn API重写代码
        String script = "console.log('Passkey Test Page Loaded');";
        webView.evaluateJavascript(script, null);
    }

    public class WebAppInterface {
        @JavascriptInterface
        public void createPasskey(String username, String rpId) {
            runOnUiThread(() -> {
                passkeyManager.createPasskey(username, rpId, new PasskeyManager.PasskeyCallback() {
                    @Override
                    public void onSuccess(String result) {
                        runOnUiThread(() -> {
                            webView.evaluateJavascript("onPasskeyResult(true, 'Passkey创建成功！')", null);
                            Toast.makeText(WebAuthnTestActivity.this, "Passkey创建成功", Toast.LENGTH_SHORT).show();
                        });
                    }

                    @Override
                    public void onError(String error) {
                        runOnUiThread(() -> {
                            webView.evaluateJavascript("onPasskeyResult(false, '创建失败: " + error + "')", null);
                            Toast.makeText(WebAuthnTestActivity.this, "创建失败: " + error, Toast.LENGTH_LONG).show();
                        });
                    }
                });
            });
        }

        @JavascriptInterface
        public void authenticatePasskey(String rpId) {
            runOnUiThread(() -> {
                passkeyManager.authenticateWithPasskey(rpId, new PasskeyManager.PasskeyCallback() {
                    @Override
                    public void onSuccess(String result) {
                        runOnUiThread(() -> {
                            webView.evaluateJavascript("onPasskeyResult(true, 'Passkey认证成功！')", null);
                            Toast.makeText(WebAuthnTestActivity.this, "Passkey认证成功", Toast.LENGTH_SHORT).show();
                        });
                    }

                    @Override
                    public void onError(String error) {
                        runOnUiThread(() -> {
                            webView.evaluateJavascript("onPasskeyResult(false, '认证失败: " + error + "')", null);
                            Toast.makeText(WebAuthnTestActivity.this, "认证失败: " + error, Toast.LENGTH_LONG).show();
                        });
                    }
                });
            });
        }
    }
}
