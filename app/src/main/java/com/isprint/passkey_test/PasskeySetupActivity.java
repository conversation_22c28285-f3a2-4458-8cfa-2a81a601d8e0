package com.isprint.passkey_test;

import android.content.Intent;
import android.os.Bundle;
import android.provider.Settings;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

public class PasskeySetupActivity extends AppCompatActivity {

    private Button btnOpenCredentialSettings;
    private Button btnTestPasskey;
    private TextView tvInstructions;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_passkey_setup);

        initViews();
        setupClickListeners();
    }

    private void initViews() {
        btnOpenCredentialSettings = findViewById(R.id.btn_open_credential_settings);
        btnTestPasskey = findViewById(R.id.btn_test_passkey);
        tvInstructions = findViewById(R.id.tv_instructions);

        updateInstructions();
    }

    private void setupClickListeners() {
        btnOpenCredentialSettings.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                openCredentialManagerSettings();
            }
        });

        btnTestPasskey.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(PasskeySetupActivity.this, ChromeTestActivity.class);
                startActivity(intent);
            }
        });
    }

    private void openCredentialManagerSettings() {
        try {
            // 尝试打开凭据管理器设置
            Intent intent = new Intent("android.settings.CREDENTIAL_MANAGER_SETTINGS");
            startActivity(intent);
            Toast.makeText(this, "请在设置中选择 'Passkey Test' 作为凭据提供者", Toast.LENGTH_LONG).show();
        } catch (Exception e) {
            try {
                // 如果上面的不可用，尝试打开安全设置
                Intent intent = new Intent(Settings.ACTION_SECURITY_SETTINGS);
                startActivity(intent);
                Toast.makeText(this, "请在安全设置中查找凭据管理器选项", Toast.LENGTH_LONG).show();
            } catch (Exception ex) {
                Toast.makeText(this, "无法打开设置，请手动进入：设置 > 安全 > 凭据管理器", Toast.LENGTH_LONG).show();
            }
        }
    }

    private void updateInstructions() {
        String instructions = "🔧 设置Passkey提供者\n\n" +
                "为了让Chrome调用我们的app而不是Google密码管理器，需要进行以下设置：\n\n" +
                "📋 设置步骤：\n\n" +
                "1️⃣ 点击「打开凭据管理器设置」\n" +
                "   • 进入系统的凭据管理器设置\n" +
                "   • 查找「Passkey Test」选项\n" +
                "   • 启用我们的app作为凭据提供者\n\n" +
                "2️⃣ 设置完成后点击「测试Passkey功能」\n" +
                "   • 进入Chrome测试页面\n" +
                "   • 在网站上测试Passkey交互\n\n" +
                "💡 重要提示：\n" +
                "• 需要Android 14或更高版本\n" +
                "• 某些设备可能不支持第三方凭据提供者\n" +
                "• 如果找不到设置选项，说明设备不支持\n\n" +
                "🔄 替代方案：\n" +
                "如果无法设置为凭据提供者，可以：\n" +
                "• 使用自动填充功能测试密码管理\n" +
                "• 在app内部测试Passkey API\n" +
                "• 等待更多设备支持第三方提供者";

        tvInstructions.setText(instructions);
    }
}
