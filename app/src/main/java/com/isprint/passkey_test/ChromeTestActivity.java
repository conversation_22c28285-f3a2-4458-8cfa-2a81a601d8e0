package com.isprint.passkey_test;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

public class ChromeTestActivity extends AppCompatActivity {

    private Button btnOpenChromeTest;
    private Button btnOpenDemoSite;
    private TextView tvInstructions;
    private PasskeyManager passkeyManager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_chrome_test);

        initViews();
        passkeyManager = new PasskeyManager(this);
        setupClickListeners();
    }

    private void initViews() {
        btnOpenChromeTest = findViewById(R.id.btn_open_chrome_test);
        btnOpenDemoSite = findViewById(R.id.btn_open_demo_site);
        tvInstructions = findViewById(R.id.tv_instructions);

        updateInstructions();
    }

    private void setupClickListeners() {
        btnOpenChromeTest.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                openChromeTestPage();
            }
        });

        btnOpenDemoSite.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                openDemoSite();
            }
        });
    }

    private void openChromeTestPage() {
        // 打开一个支持WebAuthn的测试网站
        String testUrl = "https://webauthn.io/";
        Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(testUrl));
        intent.setPackage("com.android.chrome"); // 强制使用Chrome
        
        try {
            startActivity(intent);
            Toast.makeText(this, "已在Chrome中打开WebAuthn测试网站", Toast.LENGTH_LONG).show();
        } catch (Exception e) {
            // 如果Chrome不可用，使用默认浏览器
            Intent fallbackIntent = new Intent(Intent.ACTION_VIEW, Uri.parse(testUrl));
            startActivity(fallbackIntent);
            Toast.makeText(this, "已在默认浏览器中打开测试网站", Toast.LENGTH_LONG).show();
        }
    }

    private void openDemoSite() {
        String demoUrl = "https://demo.i-sprint.com/usossf";
        Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(demoUrl));
        intent.setPackage("com.android.chrome");
        
        try {
            startActivity(intent);
            Toast.makeText(this, "已在Chrome中打开同事的demo网站", Toast.LENGTH_LONG).show();
        } catch (Exception e) {
            Intent fallbackIntent = new Intent(Intent.ACTION_VIEW, Uri.parse(demoUrl));
            startActivity(fallbackIntent);
            Toast.makeText(this, "已在默认浏览器中打开demo网站", Toast.LENGTH_LONG).show();
        }
    }



    private void updateInstructions() {
        String instructions = "🌐 Chrome浏览器Passkey测试指南\n\n" +
                "📋 正确的测试步骤：\n\n" +
                "1️⃣ 直接点击「打开WebAuthn测试网站」\n" +
                "   • 在Chrome中访问webauthn.io\n" +
                "   • 这是一个标准的WebAuthn测试网站\n\n" +
                "2️⃣ 在网站上创建Passkey：\n" +
                "   • 点击网站的「Register」按钮\n" +
                "   • 填写用户名（如：<EMAIL>）\n" +
                "   • Chrome会调用你的app进行Passkey创建\n" +
                "   • 完成生物识别验证\n\n" +
                "3️⃣ 测试Passkey登录：\n" +
                "   • 点击网站的「Authenticate」按钮\n" +
                "   • Chrome会调用你的app进行认证\n" +
                "   • 完成生物识别验证\n\n" +
                "4️⃣ 测试同事的网站：\n" +
                "   • 点击「打开Demo网站」\n" +
                "   • 在网站上尝试Passkey功能\n\n" +
                "💡 重要提示：\n" +
                "• 不要在app中预先创建Passkey\n" +
                "• 让网站来触发Passkey创建\n" +
                "• Chrome会自动调用你的app\n" +
                "• 确保设备支持生物识别\n\n" +
                "⚠️ 如果遇到RP ID错误：\n" +
                "• 这是正常的，因为域名验证\n" +
                "• 直接在网站上操作即可";

        tvInstructions.setText(instructions);
    }
}
