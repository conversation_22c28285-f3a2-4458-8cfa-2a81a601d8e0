package com.isprint.passkey_test;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

public class MainActivity extends AppCompatActivity {

    private Button btnPasswordManager;
    private Button btnPasskeyDemo;
    private Button btnWebDemo;
    private Button btnSettings;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        initViews();
        setupClickListeners();
    }

    private void initViews() {
        btnPasswordManager = findViewById(R.id.btn_password_manager);
        btnPasskeyDemo = findViewById(R.id.btn_passkey_demo);
        btnWebDemo = findViewById(R.id.btn_web_demo);
        btnSettings = findViewById(R.id.btn_settings);
    }

    private void setupClickListeners() {
        btnPasswordManager.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 提示用户先设置自动填充服务
                if (!isAutofillServiceEnabled()) {
                    Toast.makeText(MainActivity.this,
                        "请先在设置中启用自动填充服务！", Toast.LENGTH_LONG).show();
                }
                Intent intent = new Intent(MainActivity.this, PasswordManagerActivity.class);
                startActivity(intent);
            }
        });

        btnPasskeyDemo.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(MainActivity.this, PasskeyActivity.class);
                startActivity(intent);
            }
        });

        btnWebDemo.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(MainActivity.this, WebViewActivity.class);
                startActivity(intent);
            }
        });

        btnSettings.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(MainActivity.this, SettingsActivity.class);
                startActivity(intent);
            }
        });
    }
}
