package com.isprint.passkey_test;

import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

public class PasskeyActivity extends AppCompatActivity {

    private EditText etUsername;
    private Button btnCreatePasskey;
    private Button btnAuthenticatePasskey;
    private TextView tvResult;
    private PasskeyManager passkeyManager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_passkey);

        initViews();
        passkeyManager = new PasskeyManager(this);
        setupClickListeners();
    }

    private void initViews() {
        etUsername = findViewById(R.id.et_username);
        btnCreatePasskey = findViewById(R.id.btn_create_passkey);
        btnAuthenticatePasskey = findViewById(R.id.btn_authenticate_passkey);
        tvResult = findViewById(R.id.tv_result);
    }

    private void setupClickListeners() {
        btnCreatePasskey.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String username = etUsername.getText().toString().trim();
                if (username.isEmpty()) {
                    Toast.makeText(PasskeyActivity.this, "请输入用户名", Toast.LENGTH_SHORT).show();
                    return;
                }

                btnCreatePasskey.setEnabled(false);
                tvResult.setText("正在创建Passkey...");

                passkeyManager.createPasskey(username, new PasskeyManager.PasskeyCallback() {
                    @Override
                    public void onSuccess(String result) {
                        runOnUiThread(() -> {
                            btnCreatePasskey.setEnabled(true);
                            tvResult.setText(result);
                            Toast.makeText(PasskeyActivity.this, "Passkey创建成功", Toast.LENGTH_SHORT).show();
                        });
                    }

                    @Override
                    public void onError(String error) {
                        runOnUiThread(() -> {
                            btnCreatePasskey.setEnabled(true);
                            tvResult.setText("错误: " + error);
                            Toast.makeText(PasskeyActivity.this, "创建失败: " + error, Toast.LENGTH_LONG).show();
                        });
                    }
                });
            }
        });

        btnAuthenticatePasskey.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String username = etUsername.getText().toString().trim();
                if (username.isEmpty()) {
                    Toast.makeText(PasskeyActivity.this, "请输入用户名", Toast.LENGTH_SHORT).show();
                    return;
                }

                btnAuthenticatePasskey.setEnabled(false);
                tvResult.setText("正在进行Passkey认证...");

                passkeyManager.authenticateWithPasskey(username, new PasskeyManager.PasskeyCallback() {
                    @Override
                    public void onSuccess(String result) {
                        runOnUiThread(() -> {
                            btnAuthenticatePasskey.setEnabled(true);
                            tvResult.setText(result);
                            Toast.makeText(PasskeyActivity.this, "认证成功", Toast.LENGTH_SHORT).show();
                        });
                    }

                    @Override
                    public void onError(String error) {
                        runOnUiThread(() -> {
                            btnAuthenticatePasskey.setEnabled(true);
                            tvResult.setText("错误: " + error);
                            Toast.makeText(PasskeyActivity.this, "认证失败: " + error, Toast.LENGTH_LONG).show();
                        });
                    }
                });
            }
        });
    }
}
