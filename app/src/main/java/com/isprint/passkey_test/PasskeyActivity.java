package com.isprint.passkey_test;

import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

public class PasskeyActivity extends AppCompatActivity {

    private EditText etUsername;
    private EditText etRpId;
    private Button btnCreatePasskey;
    private Button btnAuthenticatePasskey;
    private TextView tvResult;
    private PasskeyManager passkeyManager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_passkey);

        initViews();
        passkeyManager = new PasskeyManager(this);
        setupClickListeners();

        // 检查是否是系统调用
        handleSystemIntent();
    }

    private void handleSystemIntent() {
        Intent intent = getIntent();
        String action = intent.getAction();

        if ("androidx.credentials.PROVIDER_ACTION_CREATE".equals(action)) {
            // 系统请求创建Passkey
            tvResult.setText("🎉 系统正在请求创建Passkey！\n\n这说明我们的app已经被识别为凭据提供者！");
            Toast.makeText(this, "系统调用：创建Passkey", Toast.LENGTH_LONG).show();
        } else if ("androidx.credentials.PROVIDER_ACTION_GET".equals(action)) {
            // 系统请求获取Passkey
            tvResult.setText("🎉 系统正在请求使用Passkey认证！\n\n这说明我们的app已经被识别为凭据提供者！");
            Toast.makeText(this, "系统调用：Passkey认证", Toast.LENGTH_LONG).show();
        }
    }

    private void initViews() {
        etUsername = findViewById(R.id.et_username);
        etRpId = findViewById(R.id.et_rp_id);
        btnCreatePasskey = findViewById(R.id.btn_create_passkey);
        btnAuthenticatePasskey = findViewById(R.id.btn_authenticate_passkey);
        tvResult = findViewById(R.id.tv_result);

        // 设置默认值
        etRpId.setText("demo.i-sprint.com");
        etUsername.setText("<EMAIL>");
    }

    private void setupClickListeners() {
        btnCreatePasskey.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String username = etUsername.getText().toString().trim();
                String rpId = etRpId.getText().toString().trim();
                
                if (username.isEmpty()) {
                    Toast.makeText(PasskeyActivity.this, "请输入用户名", Toast.LENGTH_SHORT).show();
                    return;
                }
                
                if (rpId.isEmpty()) {
                    Toast.makeText(PasskeyActivity.this, "请输入域名", Toast.LENGTH_SHORT).show();
                    return;
                }

                btnCreatePasskey.setEnabled(false);
                tvResult.setText("正在创建Passkey...\n\n这将调用系统的Credential Manager API，\n可能会弹出Google密码管理器界面。");

                passkeyManager.createPasskey(username, rpId, new PasskeyManager.PasskeyCallback() {
                    @Override
                    public void onSuccess(String result) {
                        runOnUiThread(() -> {
                            btnCreatePasskey.setEnabled(true);
                            tvResult.setText("✅ " + result);
                            Toast.makeText(PasskeyActivity.this, "Passkey创建成功！", Toast.LENGTH_SHORT).show();
                        });
                    }

                    @Override
                    public void onError(String error) {
                        runOnUiThread(() -> {
                            btnCreatePasskey.setEnabled(true);
                            tvResult.setText("❌ 错误: " + error);
                            Toast.makeText(PasskeyActivity.this, "创建失败: " + error, Toast.LENGTH_LONG).show();
                        });
                    }
                });
            }
        });

        btnAuthenticatePasskey.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String rpId = etRpId.getText().toString().trim();
                
                if (rpId.isEmpty()) {
                    Toast.makeText(PasskeyActivity.this, "请输入域名", Toast.LENGTH_SHORT).show();
                    return;
                }

                btnAuthenticatePasskey.setEnabled(false);
                tvResult.setText("正在进行Passkey认证...\n\n这将调用系统的Credential Manager API，\n可能会弹出Google密码管理器界面。");

                passkeyManager.authenticateWithPasskey(rpId, new PasskeyManager.PasskeyCallback() {
                    @Override
                    public void onSuccess(String result) {
                        runOnUiThread(() -> {
                            btnAuthenticatePasskey.setEnabled(true);
                            tvResult.setText("✅ " + result);
                            Toast.makeText(PasskeyActivity.this, "Passkey认证成功！", Toast.LENGTH_SHORT).show();
                        });
                    }

                    @Override
                    public void onError(String error) {
                        runOnUiThread(() -> {
                            btnAuthenticatePasskey.setEnabled(true);
                            tvResult.setText("❌ 错误: " + error);
                            Toast.makeText(PasskeyActivity.this, "认证失败: " + error, Toast.LENGTH_LONG).show();
                        });
                    }
                });
            }
        });
    }
}
