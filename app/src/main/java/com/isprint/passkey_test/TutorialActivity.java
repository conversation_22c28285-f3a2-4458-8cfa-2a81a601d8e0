package com.isprint.passkey_test;

import android.content.Intent;
import android.os.Bundle;
import android.provider.Settings;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

public class TutorialActivity extends AppCompatActivity {

    private TextView tvStep1, tvStep2, tvStep3, tvStep4;
    private Button btnStep1, btnStep2, btnStep3, btnStep4;
    private Button btnTestAutofill;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_tutorial);

        initViews();
        setupClickListeners();
        updateStepStatus();
    }

    @Override
    protected void onResume() {
        super.onResume();
        updateStepStatus();
    }

    private void initViews() {
        tvStep1 = findViewById(R.id.tv_step1);
        tvStep2 = findViewById(R.id.tv_step2);
        tvStep3 = findViewById(R.id.tv_step3);
        tvStep4 = findViewById(R.id.tv_step4);
        
        btnStep1 = findViewById(R.id.btn_step1);
        btnStep2 = findViewById(R.id.btn_step2);
        btnStep3 = findViewById(R.id.btn_step3);
        btnStep4 = findViewById(R.id.btn_step4);
        
        btnTestAutofill = findViewById(R.id.btn_test_autofill);
    }

    private void setupClickListeners() {
        btnStep1.setOnClickListener(v -> {
            try {
                Intent intent = new Intent(Settings.ACTION_REQUEST_SET_AUTOFILL_SERVICE);
                startActivity(intent);
                Toast.makeText(this, "请选择 'Passkey Test' 作为自动填充服务", Toast.LENGTH_LONG).show();
            } catch (Exception e) {
                Toast.makeText(this, "无法打开设置，请手动进入：设置 > 系统 > 语言和输入法 > 自动填充服务", Toast.LENGTH_LONG).show();
            }
        });

        btnStep2.setOnClickListener(v -> {
            Intent intent = new Intent(this, PasswordManagerActivity.class);
            startActivity(intent);
        });

        btnStep3.setOnClickListener(v -> {
            Intent intent = new Intent(this, PasskeyActivity.class);
            startActivity(intent);
        });

        btnStep4.setOnClickListener(v -> {
            Intent intent = new Intent(this, WebViewActivity.class);
            startActivity(intent);
        });

        btnTestAutofill.setOnClickListener(v -> {
            // 打开一个简单的登录测试页面
            Intent intent = new Intent(this, TestLoginActivity.class);
            startActivity(intent);
        });
    }

    private void updateStepStatus() {
        // 检查自动填充服务状态
        boolean autofillEnabled = isAutofillServiceEnabled();
        tvStep1.setText(autofillEnabled ? "✅ 自动填充服务已启用" : "❌ 自动填充服务未启用");
        btnStep1.setText(autofillEnabled ? "重新设置" : "启用服务");

        // 检查是否有保存的密码
        boolean hasPasswords = hasStoredPasswords();
        tvStep2.setText(hasPasswords ? "✅ 已保存密码" : "❌ 未保存任何密码");

        // 检查生物识别
        boolean biometricAvailable = isBiometricAvailable();
        tvStep3.setText(biometricAvailable ? "✅ 生物识别可用" : "❌ 生物识别不可用");

        // 更新测试按钮状态
        btnTestAutofill.setEnabled(autofillEnabled && hasPasswords);
        btnTestAutofill.setText(btnTestAutofill.isEnabled() ? "测试自动填充" : "请先完成前面的步骤");
    }

    private boolean isAutofillServiceEnabled() {
        try {
            String enabledServices = Settings.Secure.getString(
                getContentResolver(), 
                Settings.Secure.ENABLED_AUTOFILL_SERVICES
            );
            return enabledServices != null && enabledServices.contains(getPackageName());
        } catch (Exception e) {
            return false;
        }
    }

    private boolean hasStoredPasswords() {
        return getSharedPreferences("password_manager", MODE_PRIVATE)
                .getAll().size() > 0;
    }

    private boolean isBiometricAvailable() {
        try {
            androidx.biometric.BiometricManager biometricManager = 
                androidx.biometric.BiometricManager.from(this);
            return biometricManager.canAuthenticate(
                androidx.biometric.BiometricManager.Authenticators.BIOMETRIC_WEAK
            ) == androidx.biometric.BiometricManager.BIOMETRIC_SUCCESS;
        } catch (Exception e) {
            return false;
        }
    }
}
