package com.isprint.passkey_test;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

public class PasswordAdapter extends RecyclerView.Adapter<PasswordAdapter.PasswordViewHolder> {

    private List<PasswordManagerActivity.PasswordEntry> passwordList;
    private OnPasswordClickListener listener;

    public interface OnPasswordClickListener {
        void onDeleteClick(PasswordManagerActivity.PasswordEntry entry);
    }

    public PasswordAdapter(List<PasswordManagerActivity.PasswordEntry> passwordList, OnPasswordClickListener listener) {
        this.passwordList = passwordList;
        this.listener = listener;
    }

    @NonNull
    @Override
    public PasswordViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_password, parent, false);
        return new PasswordViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull PasswordViewHolder holder, int position) {
        PasswordManagerActivity.PasswordEntry entry = passwordList.get(position);
        holder.bind(entry, listener);
    }

    @Override
    public int getItemCount() {
        return passwordList.size();
    }

    static class PasswordViewHolder extends RecyclerView.ViewHolder {
        private TextView tvWebsite;
        private TextView tvUsername;
        private TextView tvPassword;
        private Button btnDelete;

        public PasswordViewHolder(@NonNull View itemView) {
            super(itemView);
            tvWebsite = itemView.findViewById(R.id.tv_website);
            tvUsername = itemView.findViewById(R.id.tv_username);
            tvPassword = itemView.findViewById(R.id.tv_password);
            btnDelete = itemView.findViewById(R.id.btn_delete);
        }

        public void bind(PasswordManagerActivity.PasswordEntry entry, OnPasswordClickListener listener) {
            tvWebsite.setText(entry.getWebsite());
            tvUsername.setText(entry.getUsername());
            
            // 隐藏密码，显示星号
            StringBuilder maskedPassword = new StringBuilder();
            for (int i = 0; i < entry.getPassword().length(); i++) {
                maskedPassword.append("*");
            }
            tvPassword.setText(maskedPassword.toString());

            btnDelete.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onDeleteClick(entry);
                }
            });

            // 点击密码字段显示/隐藏密码
            tvPassword.setOnClickListener(v -> {
                if (tvPassword.getText().toString().contains("*")) {
                    tvPassword.setText(entry.getPassword());
                } else {
                    tvPassword.setText(maskedPassword.toString());
                }
            });
        }
    }
}
