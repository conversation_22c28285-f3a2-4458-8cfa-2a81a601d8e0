package com.isprint.passkey_test;

import android.content.Intent;
import android.os.Bundle;
import android.provider.Settings;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

public class SettingsActivity extends AppCompatActivity {

    private Button btnAutofillSettings;
    private Button btnCredentialSettings;
    private Button btnBiometricSettings;
    private TextView tvInstructions;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_settings);

        initViews();
        setupClickListeners();
        updateInstructions();
    }

    private void initViews() {
        btnAutofillSettings = findViewById(R.id.btn_autofill_settings);
        btnCredentialSettings = findViewById(R.id.btn_credential_settings);
        btnBiometricSettings = findViewById(R.id.btn_biometric_settings);
        tvInstructions = findViewById(R.id.tv_instructions);
    }

    private void setupClickListeners() {
        btnAutofillSettings.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                openAutofillSettings();
            }
        });

        btnCredentialSettings.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                openCredentialSettings();
            }
        });

        btnBiometricSettings.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                openBiometricSettings();
            }
        });
    }

    private void openAutofillSettings() {
        try {
            Intent intent = new Intent(Settings.ACTION_REQUEST_SET_AUTOFILL_SERVICE);
            startActivity(intent);
        } catch (Exception e) {
            // 如果上面的Intent不可用，尝试打开通用的自动填充设置
            try {
                Intent intent = new Intent("android.settings.AUTOFILL_SETTINGS");
                startActivity(intent);
            } catch (Exception ex) {
                Toast.makeText(this, "无法打开自动填充设置", Toast.LENGTH_LONG).show();
            }
        }
    }

    private void openCredentialSettings() {
        try {
            // 尝试打开凭据管理器设置
            Intent intent = new Intent("android.settings.CREDENTIAL_MANAGER_SETTINGS");
            startActivity(intent);
        } catch (Exception e) {
            // 如果不可用，打开安全设置
            try {
                Intent intent = new Intent(Settings.ACTION_SECURITY_SETTINGS);
                startActivity(intent);
            } catch (Exception ex) {
                Toast.makeText(this, "无法打开凭据设置", Toast.LENGTH_LONG).show();
            }
        }
    }

    private void openBiometricSettings() {
        try {
            Intent intent = new Intent(Settings.ACTION_BIOMETRIC_ENROLL);
            startActivity(intent);
        } catch (Exception e) {
            try {
                Intent intent = new Intent(Settings.ACTION_FINGERPRINT_ENROLL);
                startActivity(intent);
            } catch (Exception ex) {
                try {
                    Intent intent = new Intent(Settings.ACTION_SECURITY_SETTINGS);
                    startActivity(intent);
                } catch (Exception exc) {
                    Toast.makeText(this, "无法打开生物识别设置", Toast.LENGTH_LONG).show();
                }
            }
        }
    }

    private void updateInstructions() {
        String instructions = "设置说明：\n\n" +
                "1. 自动填充设置：\n" +
                "   - 点击「自动填充设置」按钮\n" +
                "   - 选择「Passkey Test」作为自动填充服务\n" +
                "   - 启用自动填充功能\n\n" +
                "2. 凭据管理器设置：\n" +
                "   - 点击「凭据管理器设置」按钮\n" +
                "   - 确保启用了Passkey功能\n" +
                "   - 配置首选的凭据提供商\n\n" +
                "3. 生物识别设置：\n" +
                "   - 点击「生物识别设置」按钮\n" +
                "   - 设置指纹或面部识别\n" +
                "   - 这是使用Passkey的前提条件\n\n" +
                "注意：某些设置可能需要Android 14或更高版本。";

        tvInstructions.setText(instructions);
    }
}
