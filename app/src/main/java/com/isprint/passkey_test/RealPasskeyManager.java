package com.isprint.passkey_test;

import android.content.Context;
import android.util.Base64;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.credentials.CreateCredentialRequest;
import androidx.credentials.CreateCredentialResponse;
import androidx.credentials.CreatePublicKeyCredentialRequest;
import androidx.credentials.CredentialManager;
import androidx.credentials.CredentialManagerCallback;
import androidx.credentials.GetCredentialRequest;
import androidx.credentials.GetCredentialResponse;
import androidx.credentials.GetPublicKeyCredentialOption;
import androidx.credentials.PublicKeyCredential;
import androidx.credentials.exceptions.CreateCredentialException;
import androidx.credentials.exceptions.GetCredentialException;

import com.google.gson.Gson;
import com.google.gson.JsonObject;

import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

public class RealPasskeyManager {

    private static final String TAG = "RealPasskeyManager";
    private final Context context;
    private final CredentialManager credentialManager;
    private final Executor executor;
    private final Gson gson;

    public RealPasskeyManager(Context context) {
        this.context = context;
        this.credentialManager = CredentialManager.create(context);
        this.executor = Executors.newSingleThreadExecutor();
        this.gson = new Gson();
    }

    public interface PasskeyCallback {
        void onSuccess(String result);
        void onError(String error);
    }

    public void createPasskey(String username, String rpId, PasskeyCallback callback) {
        Log.d(TAG, "Creating real passkey for user: " + username + " on domain: " + rpId);

        try {
            // 生成挑战
            byte[] challenge = generateChallenge();
            String challengeBase64 = Base64.encodeToString(challenge, Base64.URL_SAFE | Base64.NO_WRAP | Base64.NO_PADDING);

            // 生成用户ID
            byte[] userId = username.getBytes(StandardCharsets.UTF_8);
            String userIdBase64 = Base64.encodeToString(userId, Base64.URL_SAFE | Base64.NO_WRAP | Base64.NO_PADDING);

            // 构建WebAuthn创建请求
            JsonObject createCredentialJson = new JsonObject();
            createCredentialJson.addProperty("challenge", challengeBase64);
            createCredentialJson.addProperty("timeout", 60000);

            // RP信息
            JsonObject rp = new JsonObject();
            rp.addProperty("id", rpId);
            rp.addProperty("name", "Passkey Test App");
            createCredentialJson.add("rp", rp);

            // 用户信息
            JsonObject user = new JsonObject();
            user.addProperty("id", userIdBase64);
            user.addProperty("name", username);
            user.addProperty("displayName", username);
            createCredentialJson.add("user", user);

            // 公钥凭据参数
            JsonObject[] pubKeyCredParams = new JsonObject[2];
            
            // ES256 算法
            JsonObject es256 = new JsonObject();
            es256.addProperty("type", "public-key");
            es256.addProperty("alg", -7);
            pubKeyCredParams[0] = es256;
            
            // RS256 算法
            JsonObject rs256 = new JsonObject();
            rs256.addProperty("type", "public-key");
            rs256.addProperty("alg", -257);
            pubKeyCredParams[1] = rs256;
            
            createCredentialJson.add("pubKeyCredParams", gson.toJsonTree(pubKeyCredParams));

            // 认证器选择
            JsonObject authenticatorSelection = new JsonObject();
            authenticatorSelection.addProperty("authenticatorAttachment", "platform");
            authenticatorSelection.addProperty("requireResidentKey", true);
            authenticatorSelection.addProperty("residentKey", "required");
            authenticatorSelection.addProperty("userVerification", "required");
            createCredentialJson.add("authenticatorSelection", authenticatorSelection);

            // 证明
            createCredentialJson.addProperty("attestation", "none");

            String requestJson = gson.toJson(createCredentialJson);
            Log.d(TAG, "Create credential request JSON: " + requestJson);

            CreatePublicKeyCredentialRequest createRequest = new CreatePublicKeyCredentialRequest(requestJson);

            credentialManager.createCredentialAsync(
                    context,
                    createRequest,
                    null,
                    executor,
                    new CredentialManagerCallback<CreateCredentialResponse, CreateCredentialException>() {
                        @Override
                        public void onResult(CreateCredentialResponse result) {
                            Log.d(TAG, "Real passkey created successfully");
                            try {
                                // CreateCredentialResponse 包含创建的凭据信息
                                String responseData = result.getData().toString();
                                Log.d(TAG, "Passkey creation response: " + responseData);
                                callback.onSuccess("真实Passkey创建成功！\n响应数据: " + responseData);
                            } catch (Exception e) {
                                Log.d(TAG, "Passkey created successfully, but couldn't extract response data");
                                callback.onSuccess("真实Passkey创建成功！");
                            }
                        }

                        @Override
                        public void onError(@NonNull CreateCredentialException e) {
                            Log.e(TAG, "Failed to create real passkey", e);
                            callback.onError("创建真实Passkey失败: " + e.getMessage());
                        }
                    }
            );

        } catch (Exception e) {
            Log.e(TAG, "Error creating passkey", e);
            callback.onError("创建Passkey时发生错误: " + e.getMessage());
        }
    }

    public void authenticateWithPasskey(String rpId, PasskeyCallback callback) {
        Log.d(TAG, "Authenticating with real passkey on domain: " + rpId);

        try {
            // 生成挑战
            byte[] challenge = generateChallenge();
            String challengeBase64 = Base64.encodeToString(challenge, Base64.URL_SAFE | Base64.NO_WRAP | Base64.NO_PADDING);

            // 构建WebAuthn获取请求
            JsonObject getCredentialJson = new JsonObject();
            getCredentialJson.addProperty("challenge", challengeBase64);
            getCredentialJson.addProperty("timeout", 60000);
            getCredentialJson.addProperty("rpId", rpId);
            getCredentialJson.addProperty("userVerification", "required");

            String requestJson = gson.toJson(getCredentialJson);
            Log.d(TAG, "Get credential request JSON: " + requestJson);

            GetPublicKeyCredentialOption getOption = new GetPublicKeyCredentialOption(requestJson);
            GetCredentialRequest getRequest = new GetCredentialRequest.Builder()
                    .addCredentialOption(getOption)
                    .build();

            credentialManager.getCredentialAsync(
                    context,
                    getRequest,
                    null,
                    executor,
                    new CredentialManagerCallback<GetCredentialResponse, GetCredentialException>() {
                        @Override
                        public void onResult(GetCredentialResponse result) {
                            Log.d(TAG, "Real passkey authentication successful");
                            if (result.getCredential() instanceof PublicKeyCredential) {
                                PublicKeyCredential credential = (PublicKeyCredential) result.getCredential();
                                String response = credential.getAuthenticationResponseJson();
                                Log.d(TAG, "Authentication response: " + response);
                                callback.onSuccess("真实Passkey认证成功！\n响应数据: " + response);
                            } else {
                                callback.onSuccess("真实Passkey认证成功！");
                            }
                        }

                        @Override
                        public void onError(@NonNull GetCredentialException e) {
                            Log.e(TAG, "Failed to authenticate with real passkey", e);
                            callback.onError("真实Passkey认证失败: " + e.getMessage());
                        }
                    }
            );

        } catch (Exception e) {
            Log.e(TAG, "Error authenticating with passkey", e);
            callback.onError("认证时发生错误: " + e.getMessage());
        }
    }

    private byte[] generateChallenge() {
        byte[] challenge = new byte[32];
        new SecureRandom().nextBytes(challenge);
        return challenge;
    }
}
