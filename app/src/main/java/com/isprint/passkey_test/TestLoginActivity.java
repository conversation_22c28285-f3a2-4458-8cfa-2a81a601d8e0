package com.isprint.passkey_test;

import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

public class TestLoginActivity extends AppCompatActivity {

    private EditText etTestUsername;
    private EditText etTestPassword;
    private Button btnTestLogin;
    private TextView tvInstructions;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_test_login);

        initViews();
        setupClickListeners();
    }

    private void initViews() {
        etTestUsername = findViewById(R.id.et_test_username);
        etTestPassword = findViewById(R.id.et_test_password);
        btnTestLogin = findViewById(R.id.btn_test_login);
        tvInstructions = findViewById(R.id.tv_instructions);

        // 设置提示文本
        etTestUsername.setHint("用户名或邮箱");
        etTestPassword.setHint("密码");

        tvInstructions.setText(
            "这是一个测试登录页面，用于验证自动填充功能：\n\n" +
            "1. 点击用户名输入框\n" +
            "2. 应该会弹出自动填充选项\n" +
            "3. 选择之前保存的账户信息\n" +
            "4. 用户名和密码会自动填入\n\n" +
            "如果没有看到自动填充选项，请检查：\n" +
            "• 是否已启用自动填充服务\n" +
            "• 是否已保存密码数据"
        );
    }

    private void setupClickListeners() {
        btnTestLogin.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String username = etTestUsername.getText().toString();
                String password = etTestPassword.getText().toString();

                if (username.isEmpty() || password.isEmpty()) {
                    Toast.makeText(TestLoginActivity.this, 
                        "请填写用户名和密码", Toast.LENGTH_SHORT).show();
                    return;
                }

                // 模拟登录成功
                Toast.makeText(TestLoginActivity.this, 
                    "模拟登录成功！\n用户名: " + username, Toast.LENGTH_LONG).show();
            }
        });
    }
}
