package com.isprint.passkey_test;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.webkit.WebChromeClient;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

public class WebViewActivity extends AppCompatActivity {

    private WebView webView;
    private static final String DEMO_URL = "https://demo.i-sprint.com/usossf";

    @SuppressLint("SetJavaScriptEnabled")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_webview);

        webView = findViewById(R.id.webview);
        setupWebView();
        loadDemoPage();
    }

    private void setupWebView() {
        WebSettings webSettings = webView.getSettings();
        webSettings.setJavaScriptEnabled(true);
        webSettings.setDomStorageEnabled(true);
        webSettings.setDatabaseEnabled(true);
        webSettings.setAllowFileAccess(true);
        webSettings.setAllowContentAccess(true);
        webSettings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);

        // 设置User Agent以支持WebAuthn
        String userAgent = webSettings.getUserAgentString();
        webSettings.setUserAgentString(userAgent + " PasskeyDemo/1.0");

        webView.setWebViewClient(new WebViewClient() {
            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                Toast.makeText(WebViewActivity.this, "页面加载完成", Toast.LENGTH_SHORT).show();
            }

            @Override
            public void onReceivedError(WebView view, int errorCode, String description, String failingUrl) {
                super.onReceivedError(view, errorCode, description, failingUrl);
                Toast.makeText(WebViewActivity.this, "加载错误: " + description, Toast.LENGTH_LONG).show();
            }
        });

        webView.setWebChromeClient(new WebChromeClient() {
            @Override
            public void onProgressChanged(WebView view, int newProgress) {
                super.onProgressChanged(view, newProgress);
                setTitle("加载中... " + newProgress + "%");
                if (newProgress == 100) {
                    setTitle("Passkey Web Demo");
                }
            }
        });

        // 添加JavaScript接口以便与原生代码交互
        webView.addJavascriptInterface(new WebAppInterface(), "Android");
    }

    private void loadDemoPage() {
        try {
            webView.loadUrl(DEMO_URL);
        } catch (Exception e) {
            Toast.makeText(this, "无法加载demo页面: " + e.getMessage(), Toast.LENGTH_LONG).show();
            // 加载本地测试页面
            loadLocalTestPage();
        }
    }

    private void loadLocalTestPage() {
        String html = "<!DOCTYPE html>\n" +
                "<html>\n" +
                "<head>\n" +
                "    <title>Passkey Test</title>\n" +
                "    <meta charset=\"UTF-8\">\n" +
                "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n" +
                "    <style>\n" +
                "        body { font-family: Arial, sans-serif; padding: 20px; }\n" +
                "        .container { max-width: 400px; margin: 0 auto; }\n" +
                "        input, button { width: 100%; padding: 10px; margin: 10px 0; }\n" +
                "        button { background: #007bff; color: white; border: none; border-radius: 5px; }\n" +
                "        .result { margin-top: 20px; padding: 10px; background: #f8f9fa; border-radius: 5px; }\n" +
                "    </style>\n" +
                "</head>\n" +
                "<body>\n" +
                "    <div class=\"container\">\n" +
                "        <h2>Passkey 测试页面</h2>\n" +
                "        <input type=\"text\" id=\"username\" placeholder=\"用户名\">\n" +
                "        <button onclick=\"createPasskey()\">创建 Passkey</button>\n" +
                "        <button onclick=\"authenticatePasskey()\">使用 Passkey 登录</button>\n" +
                "        <div id=\"result\" class=\"result\">等待操作...</div>\n" +
                "    </div>\n" +
                "\n" +
                "    <script>\n" +
                "        function createPasskey() {\n" +
                "            const username = document.getElementById('username').value;\n" +
                "            if (!username) {\n" +
                "                document.getElementById('result').innerHTML = '请输入用户名';\n" +
                "                return;\n" +
                "            }\n" +
                "\n" +
                "            document.getElementById('result').innerHTML = '正在创建 Passkey...';\n" +
                "\n" +
                "            if (navigator.credentials && navigator.credentials.create) {\n" +
                "                const challenge = new Uint8Array(32);\n" +
                "                crypto.getRandomValues(challenge);\n" +
                "\n" +
                "                const createOptions = {\n" +
                "                    publicKey: {\n" +
                "                        challenge: challenge,\n" +
                "                        rp: { id: 'demo.i-sprint.com', name: 'I-Sprint Demo' },\n" +
                "                        user: {\n" +
                "                            id: new TextEncoder().encode(username),\n" +
                "                            name: username,\n" +
                "                            displayName: username\n" +
                "                        },\n" +
                "                        pubKeyCredParams: [{ alg: -7, type: 'public-key' }],\n" +
                "                        authenticatorSelection: {\n" +
                "                            authenticatorAttachment: 'platform',\n" +
                "                            userVerification: 'required'\n" +
                "                        },\n" +
                "                        timeout: 60000,\n" +
                "                        attestation: 'none'\n" +
                "                    }\n" +
                "                };\n" +
                "\n" +
                "                navigator.credentials.create(createOptions)\n" +
                "                    .then(credential => {\n" +
                "                        document.getElementById('result').innerHTML = 'Passkey 创建成功！';\n" +
                "                    })\n" +
                "                    .catch(error => {\n" +
                "                        document.getElementById('result').innerHTML = '创建失败: ' + error.message;\n" +
                "                    });\n" +
                "            } else {\n" +
                "                document.getElementById('result').innerHTML = '浏览器不支持 WebAuthn';\n" +
                "            }\n" +
                "        }\n" +
                "\n" +
                "        function authenticatePasskey() {\n" +
                "            document.getElementById('result').innerHTML = '正在进行 Passkey 认证...';\n" +
                "\n" +
                "            if (navigator.credentials && navigator.credentials.get) {\n" +
                "                const challenge = new Uint8Array(32);\n" +
                "                crypto.getRandomValues(challenge);\n" +
                "\n" +
                "                const getOptions = {\n" +
                "                    publicKey: {\n" +
                "                        challenge: challenge,\n" +
                "                        rpId: 'demo.i-sprint.com',\n" +
                "                        userVerification: 'required',\n" +
                "                        timeout: 60000\n" +
                "                    }\n" +
                "                };\n" +
                "\n" +
                "                navigator.credentials.get(getOptions)\n" +
                "                    .then(credential => {\n" +
                "                        document.getElementById('result').innerHTML = 'Passkey 认证成功！';\n" +
                "                    })\n" +
                "                    .catch(error => {\n" +
                "                        document.getElementById('result').innerHTML = '认证失败: ' + error.message;\n" +
                "                    });\n" +
                "            } else {\n" +
                "                document.getElementById('result').innerHTML = '浏览器不支持 WebAuthn';\n" +
                "            }\n" +
                "        }\n" +
                "    </script>\n" +
                "</body>\n" +
                "</html>";

        webView.loadData(html, "text/html", "UTF-8");
    }

    @Override
    public void onBackPressed() {
        if (webView.canGoBack()) {
            webView.goBack();
        } else {
            super.onBackPressed();
        }
    }

    // JavaScript接口类
    public class WebAppInterface {
        @android.webkit.JavascriptInterface
        public void showToast(String message) {
            runOnUiThread(() -> Toast.makeText(WebViewActivity.this, message, Toast.LENGTH_SHORT).show());
        }
    }
}
