package com.isprint.passkey_test;

import android.app.assist.AssistStructure;
import android.content.Context;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.os.CancellationSignal;
import android.service.autofill.AutofillService;
import android.service.autofill.FillCallback;
import android.service.autofill.FillContext;
import android.service.autofill.FillRequest;
import android.service.autofill.FillResponse;
import android.service.autofill.SaveCallback;
import android.service.autofill.SaveRequest;
import android.util.Log;
import android.view.autofill.AutofillId;
import android.view.autofill.AutofillValue;
import android.widget.RemoteViews;

import androidx.annotation.NonNull;

import java.util.ArrayList;
import java.util.List;

public class MyAutofillService extends AutofillService {

    private static final String TAG = "MyAutofillService";
    private static final String PREFS_NAME = "autofill_prefs";

    @Override
    public void onFillRequest(@NonNull FillRequest request, @NonNull CancellationSignal cancellationSignal, @NonNull FillCallback callback) {
        Log.d(TAG, "onFillRequest called");

        List<FillContext> contexts = request.getFillContexts();
        AssistStructure structure = contexts.get(contexts.size() - 1).getStructure();

        // 解析结构，查找用户名和密码字段
        ParsedStructure parsedStructure = parseStructure(structure);

        if (parsedStructure.usernameId != null || parsedStructure.passwordId != null) {
            // 创建填充响应
            FillResponse response = createFillResponse(parsedStructure);
            callback.onSuccess(response);
        } else {
            callback.onSuccess(null);
        }
    }

    @Override
    public void onSaveRequest(@NonNull SaveRequest request, @NonNull SaveCallback callback) {
        Log.d(TAG, "onSaveRequest called");

        List<FillContext> contexts = request.getFillContexts();
        AssistStructure structure = contexts.get(contexts.size() - 1).getStructure();

        // 解析并保存用户输入的凭据
        ParsedStructure parsedStructure = parseStructure(structure);
        String username = getValueFromStructure(structure, parsedStructure.usernameId);
        String password = getValueFromStructure(structure, parsedStructure.passwordId);

        if (username != null && password != null) {
            saveCredentials(username, password);
            callback.onSuccess();
        } else {
            callback.onFailure("无法保存凭据");
        }
    }

    private ParsedStructure parseStructure(AssistStructure structure) {
        ParsedStructure result = new ParsedStructure();

        for (int i = 0; i < structure.getWindowNodeCount(); i++) {
            AssistStructure.WindowNode windowNode = structure.getWindowNodeAt(i);
            parseNode(windowNode.getRootViewNode(), result);
        }

        return result;
    }

    private void parseNode(AssistStructure.ViewNode node, ParsedStructure result) {
        String[] usernameHints = {"username", "email", "user"};
        String[] passwordHints = {"password", "pwd"};

        String hint = node.getHint();
        String inputType = node.getInputType() + "";

        // 检查是否为用户名字段
        if (hint != null) {
            for (String usernameHint : usernameHints) {
                if (hint.toLowerCase().contains(usernameHint)) {
                    result.usernameId = node.getAutofillId();
                    break;
                }
            }

            for (String passwordHint : passwordHints) {
                if (hint.toLowerCase().contains(passwordHint)) {
                    result.passwordId = node.getAutofillId();
                    break;
                }
            }
        }

        // 检查输入类型
        if (inputType.contains("129")) { // TYPE_TEXT_VARIATION_PASSWORD
            result.passwordId = node.getAutofillId();
        }

        // 递归检查子节点
        for (int i = 0; i < node.getChildCount(); i++) {
            parseNode(node.getChildAt(i), result);
        }
    }

    private FillResponse createFillResponse(ParsedStructure parsedStructure) {
        FillResponse.Builder responseBuilder = new FillResponse.Builder();

        // 获取保存的凭据
        List<Credential> credentials = getSavedCredentials();

        for (Credential credential : credentials) {
            // 创建数据集
            android.service.autofill.Dataset.Builder datasetBuilder = new android.service.autofill.Dataset.Builder();

            // 创建展示视图
            RemoteViews presentation = new RemoteViews(getPackageName(), R.layout.autofill_item);
            presentation.setTextViewText(R.id.text_username, credential.username);

            if (parsedStructure.usernameId != null) {
                datasetBuilder.setValue(parsedStructure.usernameId,
                        AutofillValue.forText(credential.username), presentation);
            }

            if (parsedStructure.passwordId != null) {
                datasetBuilder.setValue(parsedStructure.passwordId,
                        AutofillValue.forText(credential.password), presentation);
            }

            responseBuilder.addDataset(datasetBuilder.build());
        }

        return responseBuilder.build();
    }

    private String getValueFromStructure(AssistStructure structure, AutofillId autofillId) {
        if (autofillId == null) return null;

        for (int i = 0; i < structure.getWindowNodeCount(); i++) {
            AssistStructure.WindowNode windowNode = structure.getWindowNodeAt(i);
            String value = getValueFromNode(windowNode.getRootViewNode(), autofillId);
            if (value != null) return value;
        }
        return null;
    }

    private String getValueFromNode(AssistStructure.ViewNode node, AutofillId targetId) {
        if (targetId.equals(node.getAutofillId())) {
            AutofillValue value = node.getAutofillValue();
            if (value != null && value.isText()) {
                return value.getTextValue().toString();
            }
        }

        for (int i = 0; i < node.getChildCount(); i++) {
            String value = getValueFromNode(node.getChildAt(i), targetId);
            if (value != null) return value;
        }
        return null;
    }

    private void saveCredentials(String username, String password) {
        SharedPreferences prefs = getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        editor.putString("username_" + username, password);
        editor.apply();
        Log.d(TAG, "Credentials saved for: " + username);
    }

    private List<Credential> getSavedCredentials() {
        List<Credential> credentials = new ArrayList<>();
        SharedPreferences prefs = getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);

        for (String key : prefs.getAll().keySet()) {
            if (key.startsWith("username_")) {
                String username = key.substring("username_".length());
                String password = prefs.getString(key, "");
                credentials.add(new Credential(username, password));
            }
        }

        return credentials;
    }

    private static class ParsedStructure {
        AutofillId usernameId;
        AutofillId passwordId;
    }

    private static class Credential {
        String username;
        String password;

        Credential(String username, String password) {
            this.username = username;
            this.password = password;
        }
    }
}
