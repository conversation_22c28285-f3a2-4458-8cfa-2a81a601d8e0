package com.isprint.passkey_test;

import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

public class RealPasskeyActivity extends AppCompatActivity {

    private EditText etUsername;
    private EditText etRpId;
    private Button btnCreateRealPasskey;
    private Button btnAuthenticateRealPasskey;
    private TextView tvResult;
    private RealPasskeyManager realPasskeyManager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_real_passkey);

        initViews();
        realPasskeyManager = new RealPasskeyManager(this);
        setupClickListeners();
    }

    private void initViews() {
        etUsername = findViewById(R.id.et_username);
        etRpId = findViewById(R.id.et_rp_id);
        btnCreateRealPasskey = findViewById(R.id.btn_create_real_passkey);
        btnAuthenticateRealPasskey = findViewById(R.id.btn_authenticate_real_passkey);
        tvResult = findViewById(R.id.tv_result);

        // 设置默认值
        etRpId.setText("demo.i-sprint.com");
        etUsername.setText("<EMAIL>");
    }

    private void setupClickListeners() {
        btnCreateRealPasskey.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String username = etUsername.getText().toString().trim();
                String rpId = etRpId.getText().toString().trim();
                
                if (username.isEmpty()) {
                    Toast.makeText(RealPasskeyActivity.this, "请输入用户名", Toast.LENGTH_SHORT).show();
                    return;
                }
                
                if (rpId.isEmpty()) {
                    Toast.makeText(RealPasskeyActivity.this, "请输入域名", Toast.LENGTH_SHORT).show();
                    return;
                }

                btnCreateRealPasskey.setEnabled(false);
                tvResult.setText("正在创建真实Passkey...\n\n这将调用系统的Credential Manager API，\n可能会弹出Google密码管理器界面。");

                realPasskeyManager.createPasskey(username, rpId, new RealPasskeyManager.PasskeyCallback() {
                    @Override
                    public void onSuccess(String result) {
                        runOnUiThread(() -> {
                            btnCreateRealPasskey.setEnabled(true);
                            tvResult.setText("✅ " + result);
                            Toast.makeText(RealPasskeyActivity.this, "真实Passkey创建成功！", Toast.LENGTH_SHORT).show();
                        });
                    }

                    @Override
                    public void onError(String error) {
                        runOnUiThread(() -> {
                            btnCreateRealPasskey.setEnabled(true);
                            tvResult.setText("❌ 错误: " + error);
                            Toast.makeText(RealPasskeyActivity.this, "创建失败: " + error, Toast.LENGTH_LONG).show();
                        });
                    }
                });
            }
        });

        btnAuthenticateRealPasskey.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String rpId = etRpId.getText().toString().trim();
                
                if (rpId.isEmpty()) {
                    Toast.makeText(RealPasskeyActivity.this, "请输入域名", Toast.LENGTH_SHORT).show();
                    return;
                }

                btnAuthenticateRealPasskey.setEnabled(false);
                tvResult.setText("正在进行真实Passkey认证...\n\n这将调用系统的Credential Manager API，\n可能会弹出Google密码管理器界面。");

                realPasskeyManager.authenticateWithPasskey(rpId, new RealPasskeyManager.PasskeyCallback() {
                    @Override
                    public void onSuccess(String result) {
                        runOnUiThread(() -> {
                            btnAuthenticateRealPasskey.setEnabled(true);
                            tvResult.setText("✅ " + result);
                            Toast.makeText(RealPasskeyActivity.this, "真实Passkey认证成功！", Toast.LENGTH_SHORT).show();
                        });
                    }

                    @Override
                    public void onError(String error) {
                        runOnUiThread(() -> {
                            btnAuthenticateRealPasskey.setEnabled(true);
                            tvResult.setText("❌ 错误: " + error);
                            Toast.makeText(RealPasskeyActivity.this, "认证失败: " + error, Toast.LENGTH_LONG).show();
                        });
                    }
                });
            }
        });
    }
}
