package com.isprint.passkey_test;

import android.content.Context;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class PasswordManagerActivity extends AppCompatActivity {

    private EditText etUsername;
    private EditText etPassword;
    private EditText etWebsite;
    private Button btnSavePassword;
    private Button btnClearAll;
    private RecyclerView recyclerView;
    private TextView tvEmpty;
    
    private PasswordAdapter adapter;
    private List<PasswordEntry> passwordList;
    private SharedPreferences prefs;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_password_manager);

        initViews();
        setupRecyclerView();
        loadPasswords();
        setupClickListeners();
    }

    private void initViews() {
        etUsername = findViewById(R.id.et_username);
        etPassword = findViewById(R.id.et_password);
        etWebsite = findViewById(R.id.et_website);
        btnSavePassword = findViewById(R.id.btn_save_password);
        btnClearAll = findViewById(R.id.btn_clear_all);
        recyclerView = findViewById(R.id.recycler_view);
        tvEmpty = findViewById(R.id.tv_empty);
        
        prefs = getSharedPreferences("password_manager", Context.MODE_PRIVATE);
    }

    private void setupRecyclerView() {
        passwordList = new ArrayList<>();
        adapter = new PasswordAdapter(passwordList, new PasswordAdapter.OnPasswordClickListener() {
            @Override
            public void onDeleteClick(PasswordEntry entry) {
                deletePassword(entry);
            }
        });
        
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        recyclerView.setAdapter(adapter);
    }

    private void setupClickListeners() {
        btnSavePassword.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                savePassword();
            }
        });

        btnClearAll.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                clearAllPasswords();
            }
        });
    }

    private void savePassword() {
        String username = etUsername.getText().toString().trim();
        String password = etPassword.getText().toString().trim();
        String website = etWebsite.getText().toString().trim();

        if (username.isEmpty() || password.isEmpty() || website.isEmpty()) {
            Toast.makeText(this, "请填写所有字段", Toast.LENGTH_SHORT).show();
            return;
        }

        // 保存到SharedPreferences
        String key = "password_" + website + "_" + username;
        SharedPreferences.Editor editor = prefs.edit();
        editor.putString(key + "_password", password);
        editor.putString(key + "_website", website);
        editor.putString(key + "_username", username);
        editor.apply();

        // 清空输入框
        etUsername.setText("");
        etPassword.setText("");
        etWebsite.setText("");

        // 重新加载密码列表
        loadPasswords();
        
        Toast.makeText(this, "密码已保存", Toast.LENGTH_SHORT).show();
    }

    private void loadPasswords() {
        passwordList.clear();
        
        Map<String, ?> allEntries = prefs.getAll();
        for (String key : allEntries.keySet()) {
            if (key.startsWith("password_") && key.endsWith("_username")) {
                String baseKey = key.substring(0, key.length() - "_username".length());
                String username = prefs.getString(key, "");
                String password = prefs.getString(baseKey + "_password", "");
                String website = prefs.getString(baseKey + "_website", "");
                
                if (!username.isEmpty() && !password.isEmpty() && !website.isEmpty()) {
                    passwordList.add(new PasswordEntry(website, username, password));
                }
            }
        }
        
        adapter.notifyDataSetChanged();
        updateEmptyView();
    }

    private void deletePassword(PasswordEntry entry) {
        String baseKey = "password_" + entry.getWebsite() + "_" + entry.getUsername();
        SharedPreferences.Editor editor = prefs.edit();
        editor.remove(baseKey + "_password");
        editor.remove(baseKey + "_website");
        editor.remove(baseKey + "_username");
        editor.apply();
        
        loadPasswords();
        Toast.makeText(this, "密码已删除", Toast.LENGTH_SHORT).show();
    }

    private void clearAllPasswords() {
        SharedPreferences.Editor editor = prefs.edit();
        Map<String, ?> allEntries = prefs.getAll();
        for (String key : allEntries.keySet()) {
            if (key.startsWith("password_")) {
                editor.remove(key);
            }
        }
        editor.apply();
        
        loadPasswords();
        Toast.makeText(this, "所有密码已清除", Toast.LENGTH_SHORT).show();
    }

    private void updateEmptyView() {
        if (passwordList.isEmpty()) {
            recyclerView.setVisibility(View.GONE);
            tvEmpty.setVisibility(View.VISIBLE);
        } else {
            recyclerView.setVisibility(View.VISIBLE);
            tvEmpty.setVisibility(View.GONE);
        }
    }

    // 密码条目数据类
    public static class PasswordEntry {
        private String website;
        private String username;
        private String password;

        public PasswordEntry(String website, String username, String password) {
            this.website = website;
            this.username = username;
            this.password = password;
        }

        public String getWebsite() { return website; }
        public String getUsername() { return username; }
        public String getPassword() { return password; }
    }
}
