@echo off
echo "获取应用SHA256指纹..."
echo "=========================="

echo "正在获取Debug版本的SHA256指纹..."
echo ""

keytool -list -v -keystore "%USERPROFILE%\.android\debug.keystore" -alias androiddebugkey -storepass android -keypass android | findstr SHA256

echo ""
echo "=========================="
echo "请将上面的SHA256指纹提供给服务端同事"
echo "用于配置Digital Asset Links"
echo ""
echo "应用包名: com.isprint.passkey_test"
echo "域名: demo.i-sprint.com"

pause
