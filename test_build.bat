@echo off
echo "测试Android项目编译..."
echo "=========================="

echo "1. 清理项目..."
call gradlew.bat clean

echo "2. 编译项目..."
call gradlew.bat assembleDebug

if %ERRORLEVEL% EQU 0 (
    echo "=========================="
    echo "✅ 编译成功！"
    echo "APK文件位置: app\build\outputs\apk\debug\app-debug.apk"
    echo ""
    echo "现在可以安装到设备："
    echo "adb install app\build\outputs\apk\debug\app-debug.apk"
) else (
    echo "=========================="
    echo "❌ 编译失败"
    echo "请检查上面的错误信息"
)

pause
