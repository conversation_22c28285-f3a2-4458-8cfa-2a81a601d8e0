@echo off
echo "构建清理后的Passkey项目..."
echo "================================"

echo "1. 清理项目..."
call gradlew.bat clean

echo "2. 编译项目..."
call gradlew.bat assembleDebug

if %ERRORLEVEL% EQU 0 (
    echo "================================"
    echo "✅ 构建成功！"
    echo ""
    echo "项目已清理完成，只保留真实的Passkey功能："
    echo "• 使用真正的Credential Manager API"
    echo "• 与Google密码管理器集成"
    echo "• 支持WebAuthn协议"
    echo ""
    echo "APK位置: app\build\outputs\apk\debug\app-debug.apk"
    echo ""
    echo "下一步："
    echo "1. 运行 get_sha256_fingerprint.bat 获取指纹"
    echo "2. 将指纹提供给服务端同事配置Digital Asset Links"
    echo "3. 安装APK并测试Passkey功能"
) else (
    echo "================================"
    echo "❌ 构建失败，请检查错误信息"
)

pause
