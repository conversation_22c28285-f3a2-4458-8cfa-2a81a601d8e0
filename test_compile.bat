@echo off
echo "测试编译修复后的项目..."
echo "================================"

echo "1. 清理项目..."
call gradlew.bat clean

echo "2. 编译项目..."
call gradlew.bat assembleDebug

if %ERRORLEVEL% EQU 0 (
    echo "================================"
    echo "✅ 编译成功！"
    echo "真实Passkey功能已添加"
    echo "APK位置: app\build\outputs\apk\debug\app-debug.apk"
    echo ""
    echo "现在可以测试真实的Credential Manager API了！"
) else (
    echo "================================"
    echo "❌ 编译失败，请检查错误信息"
)

pause
