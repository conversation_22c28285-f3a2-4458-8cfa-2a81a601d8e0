@echo off
echo "检查Android项目编译错误..."
echo "================================"

echo "1. 清理项目..."
call gradlew.bat clean

echo "2. 尝试编译..."
call gradlew.bat assembleDebug > build_log.txt 2>&1

echo "3. 检查编译结果..."
if %ERRORLEVEL% EQU 0 (
    echo "编译成功！"
    echo "APK位置: app\build\outputs\apk\debug\app-debug.apk"
) else (
    echo "编译失败，错误信息："
    echo "================================"
    type build_log.txt
    echo "================================"
    echo "错误日志已保存到 build_log.txt"
)

pause
