# Android 密码填充和 Passkey 演示应用

这是一个演示Android密码自动填充和真实Passkey功能的示例应用，使用Credential Manager API与Google密码管理器集成。

## 功能特性

### 1. 密码管理器
- 本地密码存储和管理
- 支持网站、用户名、密码的保存
- 密码列表查看和删除功能
- 点击密码字段可显示/隐藏密码

### 2. 自动填充服务
- 实现了Android AutofillService
- 自动识别用户名和密码字段
- 提供保存的凭据供选择
- 支持保存新的登录凭据

### 3. Passkey 演示
- 使用生物识别技术模拟Passkey功能
- 支持创建和认证Passkey
- 本地存储Passkey信息
- 生物识别验证集成

### 4. 网页交互
- 内置WebView支持
- 本地测试页面，演示WebAuthn API
- 支持与外部网站交互

### 5. 设置页面
- 快速访问系统自动填充设置
- 生物识别设置引导
- 凭据管理器设置

## 技术实现

### 依赖库
- `androidx.biometric:biometric` - 生物识别认证
- `androidx.cardview:cardview` - 卡片视图
- `androidx.recyclerview:recyclerview` - 列表显示
- `androidx.webkit:webkit` - WebView支持
- `com.google.code.gson:gson` - JSON处理

### 核心组件

#### MyAutofillService
- 继承自`AutofillService`
- 解析应用界面结构，识别用户名和密码字段
- 提供保存的凭据供用户选择
- 支持保存新的登录信息

#### PasskeyManager
- 使用生物识别技术模拟Passkey功能
- 本地存储Passkey信息
- 提供创建和认证接口

#### PasswordManagerActivity
- 密码的增删查改功能
- 使用SharedPreferences进行本地存储
- RecyclerView展示密码列表

## 使用说明

### 1. 首次设置
1. 打开应用，点击"设置"
2. 点击"自动填充设置"，选择本应用作为自动填充服务
3. 点击"生物识别设置"，设置指纹或面部识别

### 2. 密码管理
1. 点击"密码管理器"
2. 输入网站、用户名、密码信息
3. 点击"保存密码"

### 3. 自动填充测试
1. 在其他应用的登录界面
2. 点击用户名或密码字段
3. 选择本应用提供的凭据

### 4. Passkey 演示
1. 点击"Passkey 演示"
2. 输入用户名，点击"创建 Passkey"
3. 完成生物识别验证
4. 使用"使用 Passkey 认证"进行登录测试

### 5. 网页交互
1. 点击"网页交互演示"
2. 在内置浏览器中测试WebAuthn功能
3. 或访问外部支持Passkey的网站

## 系统要求

- Android 8.0 (API 26) 或更高版本
- 支持生物识别的设备（用于Passkey功能）
- 建议Android 14或更高版本以获得最佳体验

## 注意事项

1. **自动填充服务**：需要在系统设置中手动启用
2. **生物识别**：Passkey功能需要设备支持并已设置生物识别
3. **权限**：应用需要网络权限用于WebView功能
4. **存储**：所有数据存储在本地，卸载应用会丢失数据

## 开发说明

这是一个演示应用，主要用于学习和测试目的：

- **PasskeyManager** 使用生物识别模拟真实的Passkey功能
- **真实的Passkey实现** 需要使用`androidx.credentials.CredentialManager` API
- **生产环境** 中应该使用更安全的存储方式，如Android Keystore
- **网络交互** 在实际应用中需要与服务器端配合实现

## 扩展功能

可以考虑添加的功能：
- 密码强度检测
- 密码生成器
- 数据导入/导出
- 云同步支持
- 更多生物识别选项
- 真实的Credential Manager API集成

## 参考资料

- [Android AutofillService 文档](https://developer.android.com/identity/autofill/autofill-services)
- [Android BiometricPrompt 文档](https://developer.android.com/reference/androidx/biometric/BiometricPrompt)
- [WebAuthn 规范](https://w3c.github.io/webauthn/)
- [FIDO Alliance Passkey 指南](https://fidoalliance.org/passkeys/)
