@echo off
echo "测试Credential Provider Service实现..."
echo "======================================"

echo "1. 清理项目..."
call gradlew.bat clean

echo "2. 编译项目..."
call gradlew.bat assembleDebug

if %ERRORLEVEL% EQU 0 (
    echo "======================================"
    echo "✅ 编译成功！"
    echo ""
    echo "新增的功能："
    echo "• 实现了真正的CredentialProviderService"
    echo "• 添加了正确的服务声明和权限"
    echo "• 使用了Android 14的标准API"
    echo ""
    echo "APK位置: app\build\outputs\apk\debug\app-debug.apk"
    echo ""
    echo "🚀 测试步骤："
    echo "1. adb install app\build\outputs\apk\debug\app-debug.apk"
    echo "2. 进入系统设置 > 密码、通行密钥和帐号 > 凭据管理器"
    echo "3. 查看是否出现 'Passkey Test' 选项"
    echo "4. 启用我们的服务"
    echo "5. 在Chrome中测试Passkey功能"
    echo ""
    echo "📱 系统要求："
    echo "• Android 14 或更高版本"
    echo "• 支持Credential Manager的设备"
    echo ""
    echo "🔍 调试命令："
    echo "adb logcat | grep MyCredentialProviderService"
) else (
    echo "======================================"
    echo "❌ 编译失败，请检查错误信息"
)

pause
