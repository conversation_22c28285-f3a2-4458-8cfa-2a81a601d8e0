@echo off
echo "修复Manifest错误后的测试构建..."
echo "=================================="

echo "1. 清理项目..."
call gradlew.bat clean

echo "2. 检查Manifest语法..."
call gradlew.bat processDebugManifest

if %ERRORLEVEL% EQU 0 (
    echo "✅ Manifest语法检查通过"
    echo ""
    echo "3. 尝试完整构建..."
    call gradlew.bat assembleDebug
    
    if %ERRORLEVEL% EQU 0 (
        echo "=================================="
        echo "🎉 构建成功！Manifest错误已修复"
        echo ""
        echo "修复的问题："
        echo "• 移除了重复的PasskeyActivity声明"
        echo "• 移除了可能不兼容的权限"
        echo "• 保留了核心的Intent Filter配置"
        echo ""
        echo "APK位置: app\build\outputs\apk\debug\app-debug.apk"
        echo ""
        echo "现在可以测试系统调用功能了！"
    ) else (
        echo "=================================="
        echo "❌ 构建失败，还有其他错误"
    )
) else (
    echo "❌ Manifest语法检查失败"
    echo "请检查AndroidManifest.xml文件"
)

pause
